<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-commons-parent</artifactId>
    <version>2.2.6.RELEASE</version>
    <relativePath>..</relativePath>
  </parent>
  <groupId>org.springframework.cloud</groupId>
  <artifactId>spring-cloud-context</artifactId>
  <version>2.2.6.RELEASE</version>
  <name>Spring Cloud Context</name>
  <description>Spring Cloud Context</description>
  <url>https://projects.spring.io/spring-cloud/spring-cloud-context/</url>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://www.spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
      <comments>Copyright 2014-2015 the original author or authors.

				Licensed under the Apache License, Version 2.0 (the "License");
				you may not use this file except in compliance with the License.
				You may obtain a copy of the License at

				https://www.apache.org/licenses/LICENSE-2.0

				Unless required by applicable law or agreed to in writing, software
				distributed under the License is distributed on an "AS IS" BASIS,
				WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
				implied.

				See the License for the specific language governing permissions and
				limitations under the License.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>dsyer</id>
      <name>Dave Syer</name>
      <email>dsyer at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>lead</role>
      </roles>
    </developer>
    <developer>
      <id>sgibb</id>
      <name>Spencer Gibb</name>
      <email>sgibb at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>lead</role>
      </roles>
    </developer>
    <developer>
      <id>mgrzejszczak</id>
      <name>Marcin Grzejszczak</name>
      <email>mgrzejszczak at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <id>rbaxter</id>
      <name>Ryan Baxter</name>
      <email>rbaxter at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <id>omaciaszeksharma</id>
      <name>Olga Maciaszek-Sharma</name>
      <email>omaciaszeksharma at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/spring-cloud/spring-cloud-commons.git/spring-cloud-context</connection>
    <developerConnection>scm:git:ssh://**************/spring-cloud/spring-cloud-commons.git/spring-cloud-context</developerConnection>
    <url>https://github.com/spring-cloud/spring-cloud-commons/spring-cloud-context</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <version>2.3.2.RELEASE</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
      <version>2.3.2.RELEASE</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>log4j-to-slf4j</artifactId>
          <groupId>org.apache.logging.log4j</groupId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <version>2.3.2.RELEASE</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
      <version>2.3.2.RELEASE</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-crypto</artifactId>
      <version>5.3.3.RELEASE</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-rsa</artifactId>
      <version>1.0.9.RELEASE</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>spring-core</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-security-crypto</artifactId>
          <groupId>org.springframework.security</groupId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.integration</groupId>
      <artifactId>spring-integration-jmx</artifactId>
      <version>5.3.2.RELEASE</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure-processor</artifactId>
      <version>2.3.2.RELEASE</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
  </dependencies>
</project>
