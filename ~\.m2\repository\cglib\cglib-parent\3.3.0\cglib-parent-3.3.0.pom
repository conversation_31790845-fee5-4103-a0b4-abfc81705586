<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
      <groupId>org.sonatype.oss</groupId>
      <artifactId>oss-parent</artifactId>
      <version>7</version>
    </parent>

    <!-- ====================================================================== -->
    <!-- P R O J E C T  D E S C R I P T I O N -->
    <!-- ====================================================================== -->
    <groupId>cglib</groupId>
    <artifactId>cglib-parent</artifactId>
    <version>3.3.0</version>
    <packaging>pom</packaging>

    <name>Code Generation Library</name>
    <url>https://github.com/cglib/cglib</url>
    <description>cglib is a powerful, high performance and quality Code Generation Library, It is used to extend JAVA classes and implements interfaces at runtime.</description>

    <scm>
        <connection>scm:git:git://github.com/cglib/cglib.git</connection>
        <developerConnection>scm:git:ssh://**************/cglib/cglib.git</developerConnection>
        <url>https://github.com/cglib/cglib</url>
    </scm>

    <issueManagement>
        <system>Github Issues</system>
        <url>https://github.com/cglib/cglib/issues</url>
    </issueManagement>

    <ciManagement>
        <system>Travis</system>
        <url>https://travis-ci.org/cglib/cglib</url>
    </ciManagement>

    <licenses>
        <license>
            <name>ASF 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <!-- ====================================================================== -->
    <!-- P R O P E R T I E S -->
    <!-- ====================================================================== -->
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <java.version.source>1.5</java.version.source>
        <java.version.target>1.5</java.version.target>
        <asm.version>7.1</asm.version>
        <ant.version>1.10.3</ant.version>
        <jmh.version>1.21</jmh.version>
        <maven-compiler-plugin.version>3.7.0</maven-compiler-plugin.version>
        <maven-resources-plugin.version>3.1.0</maven-resources-plugin.version>
        <maven-javadoc-plugin.version>3.0.1</maven-javadoc-plugin.version>
        <maven-jar-plugin.version>3.1.0</maven-jar-plugin.version>
        <java.compiler.argument />

        <java.test.compiler.argument>${java.compiler.argument}</java.test.compiler.argument>
        <gpg.skip>true</gpg.skip>
    </properties>

    <!-- ====================================================================== -->
    <!-- M O D U L E S -->
    <!-- ====================================================================== -->
    <modules>
        <module>cglib</module>
        <module>cglib-nodep</module>
        <module>cglib-sample</module>
        <module>cglib-integration-test</module>
        <module>cglib-jmh</module>
    </modules>

    <!-- ====================================================================== -->
    <!-- P R O F I L E S -->
    <!-- ====================================================================== -->
    <profiles>
        <profile>
            <id>java8</id>
            <activation>
                <jdk>[1.8,)</jdk>
            </activation>
            <properties>
                <java.test.compiler.argument>-parameters</java.test.compiler.argument>
            </properties>
        </profile>
        <profile>
            <id>java9</id>
            <activation>
                <jdk>[1.9,)</jdk>
            </activation>
            <properties>
                <java.version.source>1.6</java.version.source>
                <java.version.target>1.6</java.version.target>
                <java.test.compiler.argument>-parameters</java.test.compiler.argument>
            </properties>
        </profile>
    </profiles>

    <!-- ====================================================================== -->
    <!-- B U I L D -->
    <!-- ====================================================================== -->
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version.source}</source>
                        <target>${java.version.target}</target>
                        <compilerArgument>${java.compiler.argument}</compilerArgument>
                        <testSource>${java.specification.version}</testSource>
                        <testTarget>${java.specification.version}</testTarget>
                        <testCompilerArgument>${java.test.compiler.argument}</testCompilerArgument>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.21.0</version>
                    <configuration>
                        <systemProperties>
                            <property>
                                <name>net.sf.cglib.test.stressHashCodes</name>
                                <value>true</value>
                            </property>
                        </systemProperties>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>2.21.0</version>
                    <configuration>
                        <systemProperties>
                            <property>
                                <name>net.sf.cglib.test.stressHashCodes</name>
                                <value>true</value>
                            </property>
                        </systemProperties>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                    <configuration>
                      <quiet>true</quiet>
                      <detectLinks>false</detectLinks>
                      <detectOfflineLinks>false</detectOfflineLinks>
                      <!--<skip>${skipJavadoc}</skip>-->
                      <use>false</use>
                      <doclint>none</doclint>
                      <additionalDependencies>
                        <additionalDependency>
                          <groupId>org.ow2.asm</groupId>
                          <artifactId>asm</artifactId>
                          <version>${asm.version}</version>
                        </additionalDependency>
                        <additionalDependency>
                          <groupId>org.apache.ant</groupId>
                          <artifactId>ant</artifactId>
                          <version>${ant.version}</version>
                        </additionalDependency>
                      </additionalDependencies>
                    </configuration>
                    <executions>
                      <execution>
                        <id>attach-javadocs</id>
                        <goals>
                          <goal>jar</goal>
                        </goals>
                      </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.sonatype.plugins</groupId>
                    <artifactId>jarjar-maven-plugin</artifactId>
                    <version>1.9</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-gpg-plugin</artifactId>
              <version>1.6</version>
              <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
    </build>

    <!-- ====================================================================== -->
    <!-- D E P E N D E N C Y  M A N A G E M E N T -->
    <!-- ====================================================================== -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>cglib</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>cglib-sample</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>${ant.version}</version>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
