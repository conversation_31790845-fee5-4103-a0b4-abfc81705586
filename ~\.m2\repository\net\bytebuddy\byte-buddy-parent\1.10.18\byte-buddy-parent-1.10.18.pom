<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>net.bytebuddy</groupId>
    <artifactId>byte-buddy-parent</artifactId>
    <version>1.10.18</version>
    <packaging>pom</packaging>

    <inceptionYear>2014</inceptionYear>

    <name>Byte Buddy (parent)</name>
    <description>
        Byte Buddy is a Java library for creating Java classes at run time.
        The parent artifact contains configuration information that concern all modules.
    </description>
    <url>https://bytebuddy.net</url>

    <!--
      There are several build profiles available:
      - extras: Creates additional artifacts containing source files and javadoc. (activated on release)
      - gpg: Sign all artifacts using gpg. (activated on release)
      - checks: Applies style checks to the source files. (activated by default, activated on release)
      - integration: Runs additional unit tests and executes static code analysis (activated on CI server)
      - android: Builds an Android test application. An Android SDK is required for doing so. (excluded from release)
      - native-compile: Compiles the native extensions required by Byte Buddy agent.

      It is also possible to build Byte Buddy against a specific byte code level. By default, Byte Buddy is Java 5 compatible
      but requires Java 6 to build and to run tests: By activating a profile javaX where X is a specific version number,
      tests and source are compiled to a differing byte code level.

      Additionally, the following reports are available via Maven:
      - jacoco:prepare-agent verify jacoco:report - Computes coverage for test suite (all modules)
      - org.pitest:pitest-maven:mutationCoverage - Runs mutation tests (all modules)
      - spotbugs:spotbugs spotbugs:gui - Runs spotbugs and shows a report in a graphical interface (module specific)
      - com.github.ferstl:jitwatch-jarscan-maven-plugin:scan - Finds all methods above HotSpot's inlining threshold
      - clirr:check - Checks for binary changes in the API
    -->

    <modules>
        <module>byte-buddy</module>
        <module>byte-buddy-dep</module>
        <module>byte-buddy-benchmark</module>
        <module>byte-buddy-agent</module>
        <module>byte-buddy-android</module>
        <module>byte-buddy-android-test</module>
        <module>byte-buddy-maven-plugin</module>
        <module>byte-buddy-gradle-plugin</module>
    </modules>

    <properties>
        <copyright.holder>Rafael Winterhalter</copyright.holder>
        <bytebuddy.extras>false</bytebuddy.extras>
        <bytebuddy.integration>false</bytebuddy.integration>
        <bytebuddy.experimental>false</bytebuddy.experimental>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sourcecode.main.version>1.5</sourcecode.main.version>
        <sourcecode.test.version>1.6</sourcecode.test.version>
        <bytecode.main.version>1.5</bytecode.main.version>
        <bytecode.test.version>1.6</bytecode.test.version>
        <pitest.target>net.bytebuddy</pitest.target>
        <asm.url>https://asm.ow2.io</asm.url>
        <version.asm>9.0</version.asm>
        <version.junit>4.13.1</version.junit>
        <version.mockito>2.23.0</version.mockito>
        <version.plugin.clean>3.0.0</version.plugin.clean>
        <version.plugin.bundle>3.5.0</version.plugin.bundle>
        <version.plugin.compiler>3.8.0</version.plugin.compiler>
        <version.plugin.install>2.5.2</version.plugin.install>
        <version.plugin.deploy>2.8.2</version.plugin.deploy>
        <version.plugin.javadoc>2.10.4</version.plugin.javadoc>
        <version.plugin.source>3.0.1</version.plugin.source>
        <version.plugin.shade>3.2.4</version.plugin.shade>
        <version.plugin.gpg>1.6</version.plugin.gpg>
        <version.plugin.jxr>2.5</version.plugin.jxr>
        <version.plugin.buildhelp>1.12</version.plugin.buildhelp>
        <version.plugin.jar>3.0.2</version.plugin.jar>
        <version.plugin.site>3.8.2</version.plugin.site>
        <version.plugin.exec>1.6.0</version.plugin.exec>
        <version.plugin.plugin>3.6.0</version.plugin.plugin>
        <version.plugin.release>2.5.3</version.plugin.release>
        <version.plugin.resources>3.0.2</version.plugin.resources>
        <version.plugin.surefire>3.0.0-M5</version.plugin.surefire>
        <version.plugin.pitest>1.2.0</version.plugin.pitest>
        <version.plugin.animal-sniffer>1.16</version.plugin.animal-sniffer>
        <version.plugin.enforcer>1.4.1</version.plugin.enforcer>
        <version.plugin.jacoco>0.8.6</version.plugin.jacoco>
        <version.plugin.coveralls>4.1.0</version.plugin.coveralls>
        <version.plugin.checkstyle>2.17</version.plugin.checkstyle>
        <version.plugin.jitwatch>1.0.1</version.plugin.jitwatch>
        <version.plugin.clirr>2.8</version.plugin.clirr>
        <version.plugin.spotbugs>********</version.plugin.spotbugs>
        <version.plugin.modulemaker>1.7</version.plugin.modulemaker>
        <version.plugin.license>3.0</version.plugin.license>
        <version.android.sdk>*******</version.android.sdk>
        <version.utility.findbugs>3.0.1</version.utility.findbugs>
        <spotbugs.skip>false</spotbugs.skip>
        <jacoco.skip>false</jacoco.skip>
        <repository.url>**************:raphw/byte-buddy.git</repository.url>
    </properties>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
            <comments>A business-friendly OSS license</comments>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>raphw</id>
            <name>Rafael Winterhalter</name>
            <email><EMAIL></email>
            <url>http://rafael.codes</url>
            <roles>
                <role>developer</role>
            </roles>
            <timezone>+1</timezone>
        </developer>
    </developers>

    <issueManagement>
        <system>github.com</system>
        <url>https://github.com/raphw/byte-buddy/issues</url>
    </issueManagement>

    <scm>
        <connection>scm:git:${repository.url}</connection>
        <developerConnection>scm:git:${repository.url}</developerConnection>
        <url>${repository.url}</url>
        <tag>byte-buddy-1.10.18</tag>
    </scm>

    <dependencies>
        <!-- Allows the suppression of spotbugs false-positives by annotations without adding an actual dependency. -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>findbugs-annotations</artifactId>
            <version>${version.utility.findbugs}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Define release properties. -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>${version.plugin.release}</version>
                <configuration>
                    <useReleaseProfile>false</useReleaseProfile>
                    <releaseProfiles>extras,gpg</releaseProfiles>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <tagNameFormat>byte-buddy-@{project.version}</tagNameFormat>
                </configuration>
            </plugin>
            <!-- Enable mutation testing. -->
            <plugin>
                <groupId>org.pitest</groupId>
                <artifactId>pitest-maven</artifactId>
                <version>${version.plugin.pitest}</version>
                <configuration>
                    <targetClasses>
                        <param>${pitest.target}.*</param>
                    </targetClasses>
                    <targetTests>
                        <param>${pitest.target}.*</param>
                    </targetTests>
                </configuration>
            </plugin>
            <!-- Configure Jacoco support for evaluating test case coverage. -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${version.plugin.jacoco}</version>
                <configuration>
                    <skip>${jacoco.skip}</skip>
                    <includes>
                        <include>net/bytebuddy/**</include>
                    </includes>
                    <excludes>
                        <exclude>net/bytebuddy/benchmark/generated/*</exclude>
                        <!-- Avoid adding synthetic members to test classes as test assert class members.  -->
                        <exclude>*Test*</exclude>
                        <exclude>*test*</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <!-- Generate coveralls reports from CI server. -->
            <plugin>
                <groupId>org.eluder.coveralls</groupId>
                <artifactId>coveralls-maven-plugin</artifactId>
                <version>${version.plugin.coveralls}</version>
                <configuration>
                    <sourceDirectories>
                        <sourceDirectory>${project.basedir}/byte-buddy-dep/src/precompiled/java</sourceDirectory>
                    </sourceDirectories>
                </configuration>
            </plugin>
            <!-- Also allow for manual spotbugs execution. Note that the generated warnings do not always apply for Byte Buddy's use case. -->
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>${version.plugin.spotbugs}</version>
                <configuration>
                    <skip>${spotbugs.skip}</skip>
                    <effort>Max</effort>
                    <threshold>Low</threshold>
                    <xmlOutput>true</xmlOutput>
                    <failOnError>false</failOnError>
                    <spotbugsXmlOutputDirectory>${project.build.directory}/spotbugs</spotbugsXmlOutputDirectory>
                </configuration>
            </plugin>
            <!-- Enable scanning for methods above the inlining threshold (JDK 7+) -->
            <plugin>
                <groupId>com.github.ferstl</groupId>
                <artifactId>jitwatch-jarscan-maven-plugin</artifactId>
                <version>${version.plugin.jitwatch}</version>
            </plugin>
            <!-- Enable scanning for binary changes between releases -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>clirr-maven-plugin</artifactId>
                <version>${version.plugin.clirr}</version>
            </plugin>
            <!-- Resolve the current year. -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>${version.plugin.buildhelp}</version>
                <executions>
                    <execution>
                        <id>timestamp-property</id>
                        <goals>
                            <goal>timestamp-property</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                            <locale>en_US</locale>
                            <name>current.year</name>
                            <pattern>yyyy</pattern>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Add license headers to all files. -->
            <plugin>
                <groupId>com.mycila</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <version>${version.plugin.license}</version>
                <inherited>false</inherited>
                <configuration>
                    <header>${project.basedir}/NOTICE</header>
                    <aggregate>true</aggregate>
                    <failIfMissing>true</failIfMissing>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <properties>
                        <current.year>${current.year}</current.year>
                        <copyright.holder>${copyright.holder}</copyright.holder>
                    </properties>
                    <includes>
                        <include>**/main/java/**/*.java</include>
                        <include>**/precompiled/java/**/*.java</include>
                        <include>**/main/c/**/*.c</include>
                    </includes>
                    <strictCheck>true</strictCheck>
                    <mapping>
                        <java>SLASHSTAR_STYLE</java>
                    </mapping>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>format</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${version.plugin.clean}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${version.plugin.jar}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${version.plugin.resources}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${version.plugin.install}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${version.plugin.surefire}</version>
                    <configuration>
                        <systemPropertyVariables>
                            <net.bytebuddy.experimental>${bytebuddy.experimental}</net.bytebuddy.experimental>
                            <net.bytebuddy.test.integration>${bytebuddy.integration}</net.bytebuddy.test.integration>
                        </systemPropertyVariables>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${version.plugin.deploy}</version>
                    <configuration>
                        <updateReleaseInfo>true</updateReleaseInfo>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${version.plugin.site}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${version.plugin.compiler}</version>
                    <inherited>true</inherited>
                    <configuration>
                        <source>${sourcecode.main.version}</source>
                        <target>${bytecode.main.version}</target>
                        <testSource>${sourcecode.test.version}</testSource>
                        <testTarget>${bytecode.test.version}</testTarget>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-plugin-plugin</artifactId>
                    <version>${version.plugin.plugin}</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.ow2.asm</groupId>
                            <artifactId>asm</artifactId>
                            <version>${version.asm}</version>
                        </dependency>
                        <dependency>
                            <groupId>org.ow2.asm</groupId>
                            <artifactId>asm-commons</artifactId>
                            <version>${version.asm}</version>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- Define explicit version to overcome problem with generated reports. -->
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>${version.plugin.jxr}</version>
            </plugin>
        </plugins>
    </reporting>

    <distributionManagement>
        <repository>
            <id>bintray</id>
            <url>https://api.bintray.com/maven/raphw/maven/ByteBuddy</url>
        </repository>
    </distributionManagement>

    <profiles>
        <!-- Runs the build with compatibility for Java 6 JVMs. -->
        <profile>
            <id>java6-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>1.6</jdk>
            </activation>
            <properties>
                <version.asm.deprecated>7.1</version.asm.deprecated>
                <version.plugin.bundle>2.5.4</version.plugin.bundle>
                <version.plugin.checkstyle>2.15</version.plugin.checkstyle>
                <version.plugin.compiler>3.6.2</version.plugin.compiler>
                <version.plugin.spotbugs>3.1.0-RC8</version.plugin.spotbugs>
                <version.plugin.exec>1.5.0</version.plugin.exec>
                <version.plugin.plugin>3.5.2</version.plugin.plugin>
                <version.plugin.shade>3.1.1</version.plugin.shade>
                <version.plugin.surefire>2.22.1</version.plugin.surefire>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
            <modules>
                <module>byte-buddy</module>
                <module>byte-buddy-dep</module>
                <module>byte-buddy-benchmark</module>
                <module>byte-buddy-agent</module>
                <module>byte-buddy-android</module>
                <module>byte-buddy-maven-plugin</module>
                <module>byte-buddy-gradle-plugin</module>
            </modules>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-plugin-plugin</artifactId>
                            <version>${version.plugin.plugin}</version>
                            <dependencies>
                                <dependency>
                                    <groupId>org.ow2.asm</groupId>
                                    <artifactId>asm</artifactId>
                                    <version>${version.asm.deprecated}</version>
                                </dependency>
                                <dependency>
                                    <groupId>org.ow2.asm</groupId>
                                    <artifactId>asm-commons</artifactId>
                                    <version>${version.asm.deprecated}</version>
                                </dependency>
                                <dependency>
                                    <groupId>org.ow2.asm</groupId>
                                    <artifactId>asm-deprecated</artifactId>
                                    <version>${version.asm.deprecated}</version>
                                </dependency>
                            </dependencies>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <!-- Runs the build with compatibility for Java 7 JVMs. -->
        <profile>
            <id>java7-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>1.7</jdk>
            </activation>
            <properties>
                <version.plugin.spotbugs>3.1.0-RC8</version.plugin.spotbugs>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 9 JVMs. -->
        <profile>
            <id>java9-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>9</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.6</sourcecode.main.version>
                <sourcecode.test.version>1.6</sourcecode.test.version>
                <bytecode.main.version>1.6</bytecode.main.version>
                <bytecode.test.version>1.6</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 10 JVMs. -->
        <profile>
            <id>java10-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>10</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.7</sourcecode.main.version>
                <sourcecode.test.version>1.7</sourcecode.test.version>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 11 JVMs. -->
        <profile>
            <id>java11-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>11</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.7</sourcecode.main.version>
                <sourcecode.test.version>1.7</sourcecode.test.version>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 12 JVMs. -->
        <profile>
            <id>java12-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>12</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.7</sourcecode.main.version>
                <sourcecode.test.version>1.7</sourcecode.test.version>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 13 JVMs. -->
        <profile>
            <id>java13-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>13</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.7</sourcecode.main.version>
                <sourcecode.test.version>1.7</sourcecode.test.version>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 14 JVMs. -->
        <profile>
            <id>java14-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>14</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.7</sourcecode.main.version>
                <sourcecode.test.version>1.7</sourcecode.test.version>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 15 JVMs. -->
        <profile>
            <id>java15-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>15</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>8</sourcecode.main.version>
                <sourcecode.test.version>8</sourcecode.test.version>
                <bytecode.main.version>8</bytecode.main.version>
                <bytecode.test.version>8</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 15 JVMs. -->
        <profile>
            <id>java16-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>16</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>8</sourcecode.main.version>
                <sourcecode.test.version>8</sourcecode.test.version>
                <bytecode.main.version>8</bytecode.main.version>
                <bytecode.test.version>8</bytecode.test.version>
                <jacoco.skip>true</jacoco.skip>
                <bytebuddy.experimental>true</bytebuddy.experimental>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 6. -->
        <profile>
            <id>java6</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>1.6</bytecode.main.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 7. -->
        <profile>
            <id>java7</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 8. -->
        <profile>
            <id>java8</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>1.8</bytecode.main.version>
                <bytecode.test.version>1.8</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 9. -->
        <profile>
            <id>java9</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>9</bytecode.main.version>
                <bytecode.test.version>9</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 10. -->
        <profile>
            <id>java10</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>10</bytecode.main.version>
                <bytecode.test.version>10</bytecode.test.version>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 11. -->
        <profile>
            <id>java11</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>11</bytecode.main.version>
                <bytecode.test.version>11</bytecode.test.version>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 12. -->
        <profile>
            <id>java12</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>12</bytecode.main.version>
                <bytecode.test.version>12</bytecode.test.version>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 13. -->
        <profile>
            <id>java13</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>13</bytecode.main.version>
                <bytecode.test.version>13</bytecode.test.version>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 14. -->
        <profile>
            <id>java14</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>14</bytecode.main.version>
                <bytecode.test.version>14</bytecode.test.version>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 15. -->
        <profile>
            <id>java15</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>15</bytecode.main.version>
                <bytecode.test.version>15</bytecode.test.version>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 16. -->
        <profile>
            <id>java16</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>15</bytecode.main.version>
                <bytecode.test.version>15</bytecode.test.version>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Creates additional artifacts that are required for deployment. -->
        <profile>
            <id>extras</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytebuddy.extras>true</bytebuddy.extras>
            </properties>
            <build>
                <plugins>
                    <!-- Create source code artifact. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${version.plugin.source}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Create javadoc artifact. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${version.plugin.javadoc}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <source>${sourcecode.main.version}</source>
                            <detectJavaApiLink>true</detectJavaApiLink>
                            <links>
                                <link>${asm.url}/javadoc</link>
                            </links>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- Sign any created artifact. (Requires configuration of gpg on the executing machine.) -->
        <profile>
            <id>gpg</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <!-- Sign artifacts. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${version.plugin.gpg}</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <gpgArguments>
                                        <arg>--pinentry-mode</arg>
                                        <arg>loopback</arg>
                                    </gpgArguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- Basic checks that are not requiring too much runtime. -->
        <profile>
            <id>checks</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <!-- Check style on build. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <version>${version.plugin.checkstyle}</version>
                        <executions>
                            <execution>
                                <phase>validate</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <configLocation>checkstyle.xml</configLocation>
                                    <consoleOutput>true</consoleOutput>
                                    <failsOnError>true</failsOnError>
                                    <excludes>**/generated/**/*</excludes>
                                    <includeResources>false</includeResources>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Check API compatibility. -->
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>animal-sniffer-maven-plugin</artifactId>
                        <version>${version.plugin.animal-sniffer}</version>
                        <executions>
                            <execution>
                                <phase>test</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <signature>
                                        <groupId>org.codehaus.mojo.signature</groupId>
                                        <artifactId>java15</artifactId>
                                        <version>1.0</version>
                                    </signature>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Make sure that Byte Buddy does never depend on ASM's tree API. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <version>${version.plugin.enforcer}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                                <configuration>
                                    <fail>true</fail>
                                    <rules>
                                        <bannedDependencies>
                                            <includes>
                                                <include>org.ow2.asm:asm-tree</include>
                                            </includes>
                                        </bannedDependencies>
                                        <requireMavenVersion>
                                            <version>[3.2.5,)</version>
                                        </requireMavenVersion>
                                        <requireJavaVersion>
                                            <version>[1.6,)</version>
                                        </requireJavaVersion>
                                    </rules>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- Integration profile that executes long-running tasks and additional static code analysis. -->
        <profile>
            <id>integration</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytebuddy.integration>true</bytebuddy.integration>
            </properties>
            <build>
                <plugins>
                    <!-- Run spotbugs if not specified differently in a module.-->
                    <plugin>
                        <groupId>com.github.spotbugs</groupId>
                        <artifactId>spotbugs-maven-plugin</artifactId>
                        <version>${version.plugin.spotbugs}</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <skip>${spotbugs.skip}</skip>
                                    <effort>Max</effort>
                                    <threshold>Low</threshold>
                                    <xmlOutput>true</xmlOutput>
                                    <failOnError>true</failOnError>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
