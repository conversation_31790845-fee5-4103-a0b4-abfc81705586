<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <groupId>org.jenkins-ci</groupId>
  <artifactId>jenkins</artifactId>
  <version>1.26</version>
  <packaging>pom</packaging>

  <name>Jenkins</name>
  <url>http://jenkins-ci.org/</url>
  <inceptionYear>2004</inceptionYear>

  <issueManagement>
    <system>JIRA</system>
    <url>http://issues.jenkins-ci.org</url>
  </issueManagement>

  <ciManagement>
    <system>Jenkins</system>
    <url>http://ci.jenkins-ci.org</url>
  </ciManagement>

  <mailingLists>
    <mailingList>
      <name>Jenkins advisories list</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
    </mailingList>
    <mailingList>
      <name>Jenkins developer discussion list</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://jenkins.361315.n4.nabble.com/Jenkins-dev-f387835.html</archive>
      <otherArchives>
        <otherArchive>http://hudson.361315.n4.nabble.com/Hudson-dev-f387835.html</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Jenkins issues list</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
    </mailingList>
    <mailingList>
      <name>Jenkins user discussion list</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://jenkins.361315.n4.nabble.com/Jenkins-users-f361316.html</archive>
      <otherArchives>
        <otherArchive>http://hudson.361315.n4.nabble.com/Hudson-users-f361316.html</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Hudson Japanese user discussion list</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://hudson.361315.n4.nabble.com/Hudson-ja-f361157.html</archive>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:git://github.com/jenkinsci/pom.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/pom.git</developerConnection>
    <url>https://github.com/jenkinsci/pom</url>
  </scm>

  

  

  <distributionManagement>
    <repository>
      <uniqueVersion>false</uniqueVersion>
      <id>maven.jenkins-ci.org</id>
      <url>http://maven.jenkins-ci.org:8081/content/repositories/releases</url>
    </repository>
    <snapshotRepository>
      <id>maven.jenkins-ci.org</id>
      <url>http://maven.jenkins-ci.org:8081/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <defaultGoal>install</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${maven-assembly-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-changelog-plugin</artifactId>
          <version>${maven-changelog-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${maven-checkstyle-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>${maven-clean-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${maven-dependency-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven-deploy-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-doap-plugin</artifactId>
          <version>${maven-doap-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-eclipse-plugin</artifactId>
          <version>${maven-eclipse-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven-enforcer-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>${maven-gpg-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-help-plugin</artifactId>
          <version>${maven-help-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-idea-plugin</artifactId>
          <version>${maven-idea-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-install-plugin</artifactId>
          <version>${maven-install-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven-javadoc-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>${maven-jxr-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>${maven-pmd-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>${maven-project-info-reports-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-reactor-plugin</artifactId>
          <version>${maven-reactor-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>${maven-release-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <version>${maven-remote-resources-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${maven-resources-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>${maven-site-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>${maven-source-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-report-plugin</artifactId>
          <version>${maven-surefire-report-plugin.version}</version>
        </plugin>
        <plugin>
          <artifactId>maven-war-plugin</artifactId>
          <version>${maven-war-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.gmaven</groupId>
          <artifactId>gmaven-plugin</artifactId>
          <version>${gmaven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>apt-maven-plugin</artifactId>
          <version>${apt-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>axistools-maven-plugin</artifactId>
          <version>${axistools-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <version>${buildnumber-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>${build-helper-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.cargo</groupId>
          <artifactId>cargo-maven2-plugin</artifactId>
          <version>${cargo-maven2-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>cobertura-maven-plugin</artifactId>
          <version>${cobertura-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>${exec-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>${findbugs-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>gwt-maven-plugin</artifactId>
          <version>${gwt-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>javancss-maven-plugin</artifactId>
          <version>${javancss-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>jdepend-maven-plugin</artifactId>
          <version>${jdepend-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>openjpa-maven-plugin</artifactId>
          <version>${openjpa-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>taglist-maven-plugin</artifactId>
          <version>${taglist-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>versions-maven-plugin</artifactId>
          <version>${versions-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>xml-maven-plugin</artifactId>
          <version>${xml-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.kohsuke.gmaven</groupId>
          <artifactId>gmaven-plugin</artifactId>
          <version>${kohsuke-gmaven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.kohsuke.stapler</groupId>
          <artifactId>maven-stapler-plugin</artifactId>
          <version>${maven-stapler-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.jenkins-ci.tools</groupId>
          <artifactId>maven-hpi-plugin</artifactId>
          <version>${maven-hpi-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.jvnet</groupId>
          <artifactId>animal-sniffer</artifactId>
          <version>${animal-sniffer.version}</version>
        </plugin>
        <plugin>
          <groupId>org.jvnet.localizer</groupId>
          <artifactId>maven-localizer-plugin</artifactId>
          <version>${maven-localizer-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.jvnet.maven-antrun-extended-plugin</groupId>
          <artifactId>maven-antrun-extended-plugin</artifactId>
          <version>${maven-antrun-extended-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.jvnet.sorcerer</groupId>
          <artifactId>maven-sorcerer-plugin</artifactId>
          <version>${maven-sorcerer-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>${lifecycle-mapping.version}</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <versionRange>${maven-enforcer-plugin.version}</versionRange>
                    <goals>
                      <goal>display-info</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <versionRange>${maven-enforcer-plugin.version}</versionRange>
                    <goals>
                      <goal>enforce</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-remote-resources-plugin</artifactId>
                    <versionRange>${maven-remote-resources-plugin.version}</versionRange>
                    <goals>
                      <goal>process</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <versionRange>${maven-war-plugin.version}</versionRange>
                    <goals>
                      <goal>exploded</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <versionRange>${build-helper-maven-plugin.version}</versionRange>
                    <goals>
                      <goal>add-resource</goal>
                      <goal>add-source</goal>
                      <goal>add-test-source</goal>
                      <goal>add-test-resource</goal>
                      <goal>regex-property</goal>
                      <goal>timestamp-property</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <execute />
                  </action>
                </pluginExecution>
                <pluginExecution>
                    <pluginExecutionFilter>
                        <groupId>com.infradna.tool</groupId>
                        <artifactId>bridge-method-injector</artifactId>
                        <versionRange>[1.4,)</versionRange>
                        <goals>
                            <goal>process</goal>
                        </goals>
                    </pluginExecutionFilter>
                    <action>
                        <execute />
                    </action>
                </pluginExecution>
                <pluginExecution>
                    <pluginExecutionFilter>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <versionRange>[2.3,)</versionRange>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                    </pluginExecutionFilter>
                    <action>
                        <ignore />
                    </action>
                </pluginExecution>
                <pluginExecution>
                    <pluginExecutionFilter>
                        <groupId>org.kohsuke</groupId>
                        <artifactId>access-modifier-checker</artifactId>
                        <versionRange>[1.0,)</versionRange>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </pluginExecutionFilter>
                    <action>
                        <ignore />
                    </action>
                </pluginExecution>
                <pluginExecution>
                    <pluginExecutionFilter>
                        <groupId>org.codehaus.gmaven</groupId>
                        <artifactId>gmaven-plugin</artifactId>
                        <versionRange>[1.3,)</versionRange>
                        <goals>
                            <goal>compile</goal>
                            <goal>testCompile</goal>
                            <goal>generateStubs</goal>
                            <goal>generateTestStubs</goal>
                        </goals>
                    </pluginExecutionFilter>
                    <action>
                        <ignore />
                    </action>
                </pluginExecution>
                <pluginExecution>
                    <pluginExecutionFilter>
                        <groupId>org.jvnet.hudson.tools</groupId>
                        <artifactId>maven-encoding-plugin</artifactId>
                        <versionRange>[1.1,)</versionRange>
                        <goals>
                            <goal>check-encoding</goal>
                        </goals>
                    </pluginExecutionFilter>
                    <action>
                        <ignore />
                    </action>
                </pluginExecution>
                <pluginExecution>
                    <pluginExecutionFilter>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>antlr-maven-plugin</artifactId>
                        <versionRange>[2.1,)</versionRange>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </pluginExecutionFilter>
                    <action>
                        <execute />
                    </action>
                </pluginExecution>
                <pluginExecution>
                    <pluginExecutionFilter>
                        <groupId>org.jvnet.localizer</groupId>
                        <artifactId>maven-localizer-plugin</artifactId>
                        <versionRange>[1.12,)</versionRange>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </pluginExecutionFilter>
                    <action>
                        <execute />
                    </action>
                </pluginExecution>
                <pluginExecution>
                    <pluginExecutionFilter>
                        <groupId>org.jenkins-ci.tools</groupId>
                        <artifactId>maven-hpi-plugin</artifactId>
                        <versionRange>[1.74,)</versionRange>
                        <goals>
                            <goal>generate-taglib-interface</goal>
                        </goals>
                    </pluginExecutionFilter>
                    <action>
                        <execute />
                    </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.mortbay.jetty</groupId>
          <artifactId>maven-jetty-plugin</artifactId>
          <version>${maven-jetty-plugin.version}</version>
        </plugin>
       <plugin>
        <groupId>com.cloudbees</groupId>
        <artifactId>maven-license-plugin</artifactId>
        <version>${maven-license-plugin.version}</version>
        </plugin>
      </plugins>

    </pluginManagement>

    <plugins>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <phase>validate</phase>
            <goals>
              <goal>display-info</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.5</source>
          <target>1.5</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-idea-plugin</artifactId>
        <configuration>
          <jdkName>JDK1.5</jdkName>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-eclipse-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>always-check-remote-repositories</id>
      <properties>
        <maven.repository.update.freqency>always</maven.repository.update.freqency>
      </properties>
    </profile>
  </profiles>

  <properties>
    <!-- By default only check remote repositories once per week -->
    <maven.repository.update.freqency>interval:10080</maven.repository.update.freqency>

    <!-- Define all plugin versions as properties so individual hierarchies can easily override -->
    <animal-sniffer.version>1.2</animal-sniffer.version>
    <apt-maven-plugin.version>1.0-alpha-4</apt-maven-plugin.version>
    <axistools-maven-plugin.version>1.4</axistools-maven-plugin.version>
    <buildnumber-maven-plugin.version>1.0</buildnumber-maven-plugin.version>
    <build-helper-maven-plugin.version>1.7</build-helper-maven-plugin.version>
    <cargo-maven2-plugin.version>1.1.2</cargo-maven2-plugin.version>
    <cobertura-maven-plugin.version>2.5.1</cobertura-maven-plugin.version>
    <exec-maven-plugin.version>1.2</exec-maven-plugin.version>
    <findbugs-maven-plugin.version>2.3.2</findbugs-maven-plugin.version>
    <gmaven-plugin.version>1.3</gmaven-plugin.version>
    <gwt-maven-plugin.version>2.3.0-1</gwt-maven-plugin.version>
    <javancss-maven-plugin.version>2.0</javancss-maven-plugin.version>
    <jdepend-maven-plugin.version>2.0-beta-2</jdepend-maven-plugin.version>
    <kohsuke-gmaven-plugin.version>1.0-rc-5-patch-2</kohsuke-gmaven-plugin.version>
    <lifecycle-mapping.version>1.0.0</lifecycle-mapping.version>
    <maven-antrun-extended-plugin.version>1.42</maven-antrun-extended-plugin.version>
    <maven-antrun-plugin.version>1.3</maven-antrun-plugin.version>
    <maven-assembly-plugin.version>2.2.1</maven-assembly-plugin.version>
    <maven-changelog-plugin.version>2.2</maven-changelog-plugin.version>
    <maven-checkstyle-plugin.version>2.6</maven-checkstyle-plugin.version>
    <maven-clean-plugin.version>2.4.1</maven-clean-plugin.version>
    <maven-compiler-plugin.version>2.3.2</maven-compiler-plugin.version>
    <maven-dependency-plugin.version>2.3</maven-dependency-plugin.version>
    <maven-deploy-plugin.version>2.6</maven-deploy-plugin.version>
    <maven-doap-plugin.version>1.1</maven-doap-plugin.version>
    <maven-eclipse-plugin.version>2.8</maven-eclipse-plugin.version>
    <maven-enforcer-plugin.version>1.0.1</maven-enforcer-plugin.version>
    <maven-gpg-plugin.version>1.3</maven-gpg-plugin.version>
    <maven-help-plugin.version>2.1.1</maven-help-plugin.version>
    <maven-hpi-plugin.version>1.74</maven-hpi-plugin.version>
    <maven-idea-plugin.version>2.2</maven-idea-plugin.version>
    <maven-install-plugin.version>2.3.1</maven-install-plugin.version>
    <maven-javadoc-plugin.version>2.8</maven-javadoc-plugin.version>
    <maven-jar-plugin.version>2.3.1</maven-jar-plugin.version>
    <maven-jetty-plugin.version>6.1.26</maven-jetty-plugin.version>
    <maven-jxr-plugin.version>2.3</maven-jxr-plugin.version>
    <maven-localizer-plugin.version>1.13</maven-localizer-plugin.version>
    <maven-pmd-plugin.version>2.5</maven-pmd-plugin.version>
    <maven-project-info-reports-plugin.version>2.4</maven-project-info-reports-plugin.version>
    <maven-reactor-plugin.version>1.0</maven-reactor-plugin.version>
    <maven-release-plugin.version>2.2.1</maven-release-plugin.version>
    <maven-remote-resources-plugin.version>1.2.1</maven-remote-resources-plugin.version>
    <maven-resources-plugin.version>2.5</maven-resources-plugin.version>
    <maven-site-plugin.version>3.0</maven-site-plugin.version>
    <maven-sorcerer-plugin.version>0.8</maven-sorcerer-plugin.version>
    <maven-source-plugin.version>2.1.2</maven-source-plugin.version>
    <maven-stapler-plugin.version>1.16</maven-stapler-plugin.version>
    <maven-surefire-plugin.version>2.9</maven-surefire-plugin.version>
    <maven-surefire-report-plugin.version>2.9</maven-surefire-report-plugin.version>
    <maven-war-plugin.version>2.1.1</maven-war-plugin.version>
    <openjpa-maven-plugin.version>1.2</openjpa-maven-plugin.version>
    <taglist-maven-plugin.version>2.4</taglist-maven-plugin.version>
    <versions-maven-plugin.version>1.2</versions-maven-plugin.version>
    <xml-maven-plugin.version>1.0</xml-maven-plugin.version>
    <maven-license-plugin.version>1.4</maven-license-plugin.version>
  </properties>
</project>
