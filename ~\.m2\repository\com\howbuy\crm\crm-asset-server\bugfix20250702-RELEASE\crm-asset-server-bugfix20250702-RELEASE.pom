<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy.crm</groupId>
    <artifactId>crm-asset-server</artifactId>
    <packaging>pom</packaging>
    <version>bugfix20250702-RELEASE</version>

    <properties>
        <java.version>1.8</java.version>
        <dubbo.version>3.2.12</dubbo.version>
        <druid.version>1.2.8</druid.version>
        <log4j.version>2.15.0</log4j.version>
        <mybatis.version>2.2.2</mybatis.version>
        <zkclient.version>0.4</zkclient.version>
        <fastjson.version>1.2.80</fastjson.version>
        <disruptor.version>3.4.2</disruptor.version>
        <zookeeper.version>3.4.13</zookeeper.version>
        <pagehelper.version>5.3.0</pagehelper.version>
        <hessian.version>4.0.7</hessian.version>
        <dubbo-rpc-hessian.version>3.3.0</dubbo-rpc-hessian.version>
        
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <log4j-over-slf4j.version>1.7.29</log4j-over-slf4j.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-cloud-alibaba.version>2.2.6.RELEASE</spring-cloud-alibaba.version>
        <redis.clients.version>2.9.3</redis.clients.version>
        
        <spring-support-alibaba.version>1.0.11</spring-support-alibaba.version>
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.howbuy-message-rocket.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-rocket.version>
        <com.howbuy.howbuy-message-amq.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-amq.version>
        <com.howbuy.howbuy-message-service.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-service.version>
        <com.howbuy.howbuy-cachemanagement.version>3.8.0-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.util.version>1.0.0-SNAPSHOT</com.howbuy.util.version>

        <com.howbuy.crm-asset.version>1.0.0-RELEASE</com.howbuy.crm-asset.version>
	    <com.howbuy.crm-asset-server.version>bugfix20250702-RELEASE</com.howbuy.crm-asset-server.version>
        <com.howbuy.crm-core-client.version>bugfix-20250519-RELEASE</com.howbuy.crm-core-client.version>
        <com.howbuy.crm-td-client.version>bugfix20250515-RELEASE</com.howbuy.crm-td-client.version>
        <com.howbuy.crm-nt-client.version>1.9.5.4-RELEASE</com.howbuy.crm-nt-client.version>
        <com.howbuy.howbuy-commons-validator.version>1.0.0-SNAPSHOT</com.howbuy.howbuy-commons-validator.version>
        <com.howbuy.base-commons.version>1.8.2.2-RELEASE</com.howbuy.base-commons.version>
        <com.howbuy.howbuy-simu-client.version>release-20250421-zp6.2.1-RELEASE</com.howbuy.howbuy-simu-client.version>
        <com.howbuy.howbuy-member-client.version>release-20250514-khhx-report-RELEASE</com.howbuy.howbuy-member-client.version>
        <com.howbuy.howbuy-fund-client.version>release-20250522-zdd042-RELEASE</com.howbuy.howbuy-fund-client.version>
        <com.howbuy.acc-common-utils.version>3.5.9-RELEASE</com.howbuy.acc-common-utils.version>
        <com.howbuy.acc-center-facade.version>3.6.3-RELEASE</com.howbuy.acc-center-facade.version>
        <com.howbuy.howbuy-interlayer-product-client.version>3.5.48-RELEASE</com.howbuy.howbuy-interlayer-product-client.version>
        <powermock.version>2.0.7</powermock.version>
        <junit.jupiter.version>5.5.2</junit.jupiter.version>
        <junit.platform.version>1.5.2</junit.platform.version>
        <com.howbuy.center-client.version>4.6.53-RELEASE</com.howbuy.center-client.version>
        <com.google.guava.version>19.0</com.google.guava.version>
        <com.howbuy.tms.high-order-center-client.version>4.0.24-RELEASE</com.howbuy.tms.high-order-center-client.version>
        <com.howbuy.tms.order-center-search-client.version>3.9.77-RELEASE</com.howbuy.tms.order-center-search-client.version>
        <com.howbuy.ftx-online-search-facade.version>1.1.32-RELEASE</com.howbuy.ftx-online-search-facade.version>
        <com.howbuy.tms.tms-common-log-pattern.version>1.0.0-RELEASE</com.howbuy.tms.tms-common-log-pattern.version>
        <com.howbuy.high-order-center-client.version>20250515001-RELEASE</com.howbuy.high-order-center-client.version>
        <com.howbuy.order-center-search-client.version>3.9.77-RELEASE</com.howbuy.order-center-search-client.version>
        <com.howbuy.howbuy_dfile.version>1.18.1-RELEASE</com.howbuy.howbuy_dfile.version>
        <com.howbuy-boot-actuator>2.1.0-RELEASE</com.howbuy-boot-actuator>
        <com.howbuy.howbuy-boot-actuator-dubbo3.version>2.2.0-RELEASE</com.howbuy.howbuy-boot-actuator-dubbo3.version>
        <com.howbuy.crm-export.version>1.8.2.10-RELEASE</com.howbuy.crm-export.version>
        <com.howbuy.ds-data-client.version>1.9.3.1-RELEASE</com.howbuy.ds-data-client.version>
<com.howbuy.crm-export-client.version>1.8.2.10-RELEASE</com.howbuy.crm-export-client.version>
</properties>

    <dependencyManagement>
        <dependencies>
            
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.howbuy.ds.data</groupId>
                <artifactId>ds-data-client</artifactId>
                <version>${com.howbuy.ds-data-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-export-client</artifactId>
                <version>${com.howbuy.crm-export-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.acc</groupId>
                <artifactId>acc-common-utils</artifactId>
                <version>${com.howbuy.acc-common-utils.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator</artifactId>
                <version>${com.howbuy-boot-actuator}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator-dubbo3</artifactId>
                <version>${com.howbuy.howbuy-boot-actuator-dubbo3.version}</version>
            </dependency>

            
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo.extensions</groupId>
                <artifactId>dubbo-rpc-hessian</artifactId>
                <version>${dubbo-rpc-hessian.version}</version>
            </dependency>
            <dependency>
                <groupId>com.caucho</groupId>
                <artifactId>hessian</artifactId>
                <version>${hessian.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-support-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.2</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${log4j-over-slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-log-pattern</artifactId>
                <version>${com.howbuy.tms.tms-common-log-pattern.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-core-client</artifactId>
                <version>${com.howbuy.crm-core-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>base-commons</artifactId>
                        <groupId>com.howbuy.crm</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.howbuy.extend</groupId>
                <artifactId>Howbuy-security</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-td-client</artifactId>
                <version>${com.howbuy.crm-td-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>base-commons</artifactId>
                        <groupId>com.howbuy.crm</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-nt-client</artifactId>
                <version>${com.howbuy.crm-nt-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>base-commons</artifactId>
                        <groupId>com.howbuy.crm</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>utils</artifactId>
                <version>${com.howbuy.util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-service</artifactId>
                <version>${com.howbuy.howbuy-message-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-amq</artifactId>
                <version>${com.howbuy.howbuy-message-amq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-rocket</artifactId>
                <version>${com.howbuy.howbuy-message-rocket.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-ccms-watcher</artifactId>
                <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-cachemanagement</artifactId>
                <version>${com.howbuy.howbuy-cachemanagement.version}</version>
            </dependency>
            
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>4.3.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>

            <dependency>
                <artifactId>howbuy-commons-validator</artifactId>
                <groupId>com.howbuy.commons.validator</groupId>
                <version>${com.howbuy.howbuy-commons-validator.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-asset-client</artifactId>
                <version>${com.howbuy.crm-asset-server.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-asset-dao</artifactId>
                <version>${com.howbuy.crm-asset-server.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-asset-service</artifactId>
                <version>${com.howbuy.crm-asset-server.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>base-commons</artifactId>
                <version>${com.howbuy.base-commons.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-simu-client</artifactId>
                <version>${com.howbuy.howbuy-simu-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-member-client</artifactId>
                <version>${com.howbuy.howbuy-member-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-fund-client</artifactId>
                <version>${com.howbuy.howbuy-fund-client.version}</version>
            </dependency>

            
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-testng</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.platform</groupId>
                <artifactId>junit-platform-runner</artifactId>
                <version>${junit.platform.version}</version>
                <scope>test</scope>
            </dependency>
            

            
            <dependency>
                <groupId>com.howbuy.acccenter</groupId>
                <artifactId>acc-center-facade</artifactId>
                <version>${com.howbuy.acc-center-facade.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>howbuy-cache-client</artifactId>
                        <groupId>com.howbuy.pa.cache</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-beanutils</artifactId>
                        <groupId>commons-beanutils</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis-spring</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.howbuy.interlayer</groupId>
                <artifactId>howbuy-interlayer-product-client</artifactId>
                <version>${com.howbuy.howbuy-interlayer-product-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cc</groupId>
                <artifactId>center-client</artifactId>
                <version>${com.howbuy.center-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${com.google.guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>high-order-center-client</artifactId>
                <version>${com.howbuy.high-order-center-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>order-center-search-client</artifactId>
                <version>${com.howbuy.order-center-search-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>ftx-online-search-facade</artifactId>
                <version>${com.howbuy.ftx-online-search-facade.version}</version>
            </dependency>

            
            
            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-service</artifactId>
                <version>${com.howbuy.howbuy_dfile.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-impl-local</artifactId>
                <version>${com.howbuy.howbuy_dfile.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-impl-webdav</artifactId>
                <version>${com.howbuy.howbuy_dfile.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

</project>