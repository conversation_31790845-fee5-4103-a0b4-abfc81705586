<project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>javax.mail</groupId>
  <artifactId>mail</artifactId>
  <version>1.4</version>
  <name>JavaMail API</name>
  <description>
    The JavaMail API provides a platform-independent and protocol-independent framework to build mail and messaging applications.
  </description>
  <url>https://glassfish.dev.java.net/javaee5/mail/</url>

  <licenses>
    <license>
      <name>Common Development and Distribution License (CDDL) v1.0</name>
      <url>https://glassfish.dev.java.net/public/CDDLv1.0.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <distributionManagement>
    <downloadUrl>https://maven-repository.dev.java.net/nonav/repository/javax.mail/jars/mail-1.4.jar</downloadUrl>
  </distributionManagement>

  <dependencies>
    <dependency>
      <groupId>javax.activation</groupId>
      <artifactId>activation</artifactId>
      <version>1.1</version>
    </dependency>
  </dependencies>

</project>
