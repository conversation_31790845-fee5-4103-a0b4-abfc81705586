<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>byte-buddy-parent</artifactId>
    <groupId>net.bytebuddy</groupId>
    <version>1.10.18</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>byte-buddy</artifactId>
  <name>Byte Buddy (without dependencies)</name>
  <description>Byte Buddy is a Java library for creating Java classes at run time.
        This artifact is a build of Byte Buddy with all ASM dependencies repackaged into its own name space.</description>
  <build>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>${version.plugin.shade}</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <shadedArtifactAttached>false</shadedArtifactAttached>
              <createDependencyReducedPom>true</createDependencyReducedPom>
              <createSourcesJar>${bytebuddy.extras}</createSourcesJar>
              <shadeSourcesContent>true</shadeSourcesContent>
              <relocations>
                <relocation>
                  <pattern>${shade.source}</pattern>
                  <shadedPattern>${shade.target}</shadedPattern>
                </relocation>
              </relocations>
              <filters>
                <filter>
                  <artifact>net.bytebuddy:byte-buddy-dep:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.ow2.asm:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                    <exclude>**/module-info.class</exclude>
                    <exclude>**/LICENSE</exclude>
                    <exclude>**/NOTICE</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.ow2.asm:asm-commons</artifact>
                  <includes>
                    <include>org/objectweb/asm/commons/AnnotationRemapper.**</include>
                    <include>org/objectweb/asm/commons/ClassRemapper.**</include>
                    <include>org/objectweb/asm/commons/FieldRemapper.**</include>
                    <include>org/objectweb/asm/commons/MethodRemapper.**</include>
                    <include>org/objectweb/asm/commons/ModuleHashesAttribute.**</include>
                    <include>org/objectweb/asm/commons/ModuleRemapper.**</include>
                    <include>org/objectweb/asm/commons/RecordComponentRemapper.**</include>
                    <include>org/objectweb/asm/commons/Remapper.**</include>
                    <include>org/objectweb/asm/commons/SignatureRemapper.**</include>
                    <include>org/objectweb/asm/commons/SimpleRemapper.**</include>
                  </includes>
                </filter>
              </filters>
              <transformers>
                <transformer>
                  <mainClass>net.bytebuddy.build.Plugin$Engine$Default</mainClass>
                </transformer>
                <transformer>
                  <resource>META-INF/LICENSE</resource>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>${version.asm}</version>
          </dependency>
          <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm-commons</artifactId>
            <version>${version.asm}</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.pitest</groupId>
        <artifactId>pitest-maven</artifactId>
        <version>${version.plugin.pitest}</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>${version.plugin.spotbugs}</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${version.plugin.jar}</version>
        <configuration>
          <archive>
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>${version.plugin.bundle}</version>
        <executions>
          <execution>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <instructions>
            <Multi-Release>true</Multi-Release>
            <Export-Package>${packages.list}</Export-Package>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <groupId>codes.rafael.modulemaker</groupId>
        <artifactId>modulemaker-maven-plugin</artifactId>
        <version>${version.plugin.modulemaker}</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>inject-module</goal>
            </goals>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>${version.asm}</version>
          </dependency>
        </dependencies>
        <configuration>
          <name>net.bytebuddy</name>
          <multirelease>true</multirelease>
          <packages>${packages.list}</packages>
          <exports>${packages.list}</exports>
          <static-requires>java.instrument,
                        jdk.unsupported,
                        net.bytebuddy.agent</static-requires>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>extras</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.0.1</version>
            <executions>
              <execution>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <skipSource>true</skipSource>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <skipSource>true</skipSource>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>2.10.4</version>
            <executions>
              <execution>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <includeDependencySources>true</includeDependencySources>
                  <dependencySourceIncludes>
                    <dependencySourceInclude>net.bytebuddy:byte-buddy-dep</dependencySourceInclude>
                  </dependencySourceIncludes>
                  <source>1.5</source>
                  <detectJavaApiLink>true</detectJavaApiLink>
                  <links>
                    <link>https://asm.ow2.io/javadoc</link>
                  </links>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <includeDependencySources>true</includeDependencySources>
              <dependencySourceIncludes>
                <dependencySourceInclude>net.bytebuddy:byte-buddy-dep</dependencySourceInclude>
              </dependencySourceIncludes>
              <source>1.5</source>
              <detectJavaApiLink>true</detectJavaApiLink>
              <links>
                <link>https://asm.ow2.io/javadoc</link>
              </links>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>findbugs-annotations</artifactId>
      <version>3.0.1</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <properties>
    <shade.source>org.objectweb.asm</shade.source>
    <packages.list>net.bytebuddy,
            net.bytebuddy.agent.builder,
            net.bytebuddy.asm,
            net.bytebuddy.build,
            net.bytebuddy.description,
            net.bytebuddy.description.annotation,
            net.bytebuddy.description.enumeration,
            net.bytebuddy.description.field,
            net.bytebuddy.description.method,
            net.bytebuddy.description.modifier,
            net.bytebuddy.description.type,
            net.bytebuddy.dynamic,
            net.bytebuddy.dynamic.loading,
            net.bytebuddy.dynamic.scaffold,
            net.bytebuddy.dynamic.scaffold.inline,
            net.bytebuddy.dynamic.scaffold.subclass,
            net.bytebuddy.implementation,
            net.bytebuddy.implementation.attribute,
            net.bytebuddy.implementation.auxiliary,
            net.bytebuddy.implementation.bind,
            net.bytebuddy.implementation.bind.annotation,
            net.bytebuddy.implementation.bytecode,
            net.bytebuddy.implementation.bytecode.assign,
            net.bytebuddy.implementation.bytecode.assign.primitive,
            net.bytebuddy.implementation.bytecode.assign.reference,
            net.bytebuddy.implementation.bytecode.collection,
            net.bytebuddy.implementation.bytecode.constant,
            net.bytebuddy.implementation.bytecode.member,
            net.bytebuddy.matcher,
            net.bytebuddy.pool,
            net.bytebuddy.utility,
            net.bytebuddy.utility.privilege,
            net.bytebuddy.utility.visitor,
            ${shade.target},
            ${shade.target}.signature,
            ${shade.target}.commons</packages.list>
    <shade.target>net.bytebuddy.jar.asm</shade.target>
  </properties>
</project>
