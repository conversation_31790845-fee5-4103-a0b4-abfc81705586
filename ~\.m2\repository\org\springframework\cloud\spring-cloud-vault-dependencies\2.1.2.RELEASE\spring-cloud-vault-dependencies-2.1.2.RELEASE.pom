<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.cloud</groupId>
		<artifactId>spring-cloud-dependencies-parent</artifactId>
		<version>2.1.6.RELEASE</version>
		<relativePath/>
		<!-- lookup parent from repository -->
	</parent>

	<artifactId>spring-cloud-vault-dependencies</artifactId>
	<version>2.1.2.RELEASE</version>
	<packaging>pom</packaging>

	<name>Spring Cloud Vault Dependencies</name>
	<description>Spring Cloud Vault Dependencies</description>

	<properties>
		<spring-vault.version>2.1.2.RELEASE</spring-vault.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<!-- Spring Vault -->
			<dependency>
				<groupId>org.springframework.vault</groupId>
				<artifactId>spring-vault-core</artifactId>
				<version>${spring-vault.version}</version>
				<!-- Provided by Spring Boot -->
				<exclusions>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-core</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-beans</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-web</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<!-- Spring Cloud Vault -->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-vault-config</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-vault-config-aws</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-vault-config-consul</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-vault-config-databases</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-vault-config-rabbitmq</artifactId>
				<version>${project.version}</version>
			</dependency>

			<!-- Starters -->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-vault-config</artifactId>
				<version>${project.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-deploy-plugin</artifactId>
					<version>2.8.2</version>
				</plugin>

				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-jar-plugin</artifactId>
					<version>3.1.0</version>
				</plugin>

				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-javadoc-plugin</artifactId>
					<version>3.0.1</version>
				</plugin>

				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-release-plugin</artifactId>
					<version>2.5.3</version>
				</plugin>

				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-source-plugin</artifactId>
					<version>3.0.1</version>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<repositories>
		<repository>
			<id>spring-snapshots</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/libs-snapshot</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>

</project>
