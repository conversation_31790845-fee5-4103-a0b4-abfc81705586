<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.springframework.security</groupId>
  <artifactId>spring-security-crypto</artifactId>
  <version>5.3.6.RELEASE</version>
  <name>spring-security-crypto</name>
  <description>spring-security-crypto</description>
  <url>https://spring.io/spring-security</url>
  <organization>
    <name>spring.io</name>
    <url>https://spring.io/</url>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>rwinch</id>
      <name>Rob Winch</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>jgrandja</id>
      <name>Joe Grandja</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/spring-projects/spring-security</connection>
    <developerConnection>scm:git:git://github.com/spring-projects/spring-security</developerConnection>
    <url>https://github.com/spring-projects/spring-security</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
      <version>1.64</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-jcl</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
      <version>5.2.11.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
      <version>4.12</version>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
      <version>3.15.0</version>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
      <version>3.3.3</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
      <version>5.2.11.RELEASE</version>
    </dependency>
  </dependencies>
</project>
