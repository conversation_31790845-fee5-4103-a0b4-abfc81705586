<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy.ds.label</groupId>
    <artifactId>ds-label</artifactId>
    <packaging>pom</packaging>
    <version>1.8.1.11-RELEASE</version>

    <properties>
        <java.version>1.8</java.version>
        <dubbo.version>3.2.12</dubbo.version>
        <druid.version>1.2.8</druid.version>
        <log4j.version>2.15.0</log4j.version>
        <slf4j-api.version>1.7.25</slf4j-api.version>
        <mybatis.version>2.2.2</mybatis.version>
        <zkclient.version>0.4</zkclient.version>
        <fastjson.version>1.2.83</fastjson.version>
        <disruptor.version>3.4.2</disruptor.version>
        <zookeeper.version>3.4.13</zookeeper.version>
        <pagehelper.version>5.3.0</pagehelper.version>
        
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <log4j-over-slf4j.version>1.7.29</log4j-over-slf4j.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-cloud-alibaba.version>2.2.6.RELEASE</spring-cloud-alibaba.version>
        
        <elasticsearch.version>7.10.1</elasticsearch.version>
        <redis.clients.version>2.9.3</redis.clients.version>
        <fst.version>2.57</fst.version>
        <flink.version>1.19.0</flink.version>
        <commons-io.version>2.11.0</commons-io.version>
        <mysql.version>8.0.30</mysql.version>
        <ehcache.version>1.3.0</ehcache.version>
        <flink.mysql.cdc.version>3.0.1</flink.mysql.cdc.version>
        
        <spring-support-alibaba.version>1.0.11</spring-support-alibaba.version>
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.howbuy-message-rocket.version>2.4.0-RELEASE</com.howbuy.howbuy-message-rocket.version>
        <com.howbuy.howbuy-message-amq.version>2.2.1-RELEASE</com.howbuy.howbuy-message-amq.version>
        <com.howbuy.howbuy-message-service.version>2.4.0-RELEASE</com.howbuy.howbuy-message-service.version>
        <com.howbuy.howbuy-cachemanagement.version>3.8.0-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.util.version>1.0.0-SNAPSHOT</com.howbuy.util.version>
        <com.howbuy-boot-actuator>2.1.0-RELEASE</com.howbuy-boot-actuator>
        <com.howbuy.tms.tms-common-log-pattern.version>1.0.0-RELEASE</com.howbuy.tms.tms-common-log-pattern.version>
        <com.howbuy.ds-label.version>1.8.1.11-RELEASE</com.howbuy.ds-label.version>
        <com.howbuy.howbuy-commons-validator.version>1.0.0-SNAPSHOT</com.howbuy.howbuy-commons-validator.version>
	<com.howbuy.howbuy-boot-actuator-dubbo3.version>2.2.0-RELEASE</com.howbuy.howbuy-boot-actuator-dubbo3.version>
</properties>

    <dependencyManagement>
        <dependencies>
            
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator</artifactId>
                <version>${com.howbuy-boot-actuator}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator-dubbo3</artifactId>
                <version>${com.howbuy.howbuy-boot-actuator-dubbo3.version}</version>
            </dependency>

            
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-support-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.2</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${log4j-over-slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-web</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-log-pattern</artifactId>
                <version>${com.howbuy.tms.tms-common-log-pattern.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>utils</artifactId>
                <version>${com.howbuy.util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-service</artifactId>
                <version>${com.howbuy.howbuy-message-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-rocket</artifactId>
                <version>${com.howbuy.howbuy-message-rocket.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-ccms-watcher</artifactId>
                <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-cachemanagement</artifactId>
                <version>${com.howbuy.howbuy-cachemanagement.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>activemq-all</artifactId>
                        <groupId>org.apache.activemq</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>net.sf.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${ehcache.version}</version>
            </dependency>
            
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${redis.clients.version}</version>
            </dependency>

            <dependency>
                <artifactId>howbuy-commons-validator</artifactId>
                <groupId>com.howbuy.commons.validator</groupId>
                <version>${com.howbuy.howbuy-commons-validator.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            

            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.elasticsearch</groupId>
                        <artifactId>elasticsearch</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-client</artifactId>
                    </exclusion>
                </exclusions>
                <version>${elasticsearch.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.ds.label</groupId>
                <artifactId>ds-label-client</artifactId>
                <version>${com.howbuy.ds-label.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.ds.label</groupId>
                <artifactId>ds-label-dao</artifactId>
                <version>${com.howbuy.ds-label.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.ds.label</groupId>
                <artifactId>ds-label-service</artifactId>
                <version>${com.howbuy.ds-label.version}</version>
            </dependency>

        </dependencies>

    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>howbuy-releases</id>
            <name>howbuy-releases</name>
            <url>http://nexus.howbuy.pa/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshots</id>
            <name>howbuy-snapshots</name>
            <url>http://nexus.howbuy.pa/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>