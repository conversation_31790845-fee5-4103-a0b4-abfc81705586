<?xml version="1.0" encoding="iso-8859-1"?>
<!--

    Copyright (c) 1997, 2020 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
			    http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.sun.activation</groupId>
    <artifactId>all</artifactId>
    <packaging>pom</packaging>
    <version>1.2.2</version>
    <name>Jakarta Activation distribution</name>
    <description>${project.name}</description>
    <url>https://github.com/eclipse-ee4j/jaf</url>

    <scm>
        <connection>scm:git:ssh://**************/eclipse/jaf.git</connection>
        <developerConnection>scm:git:ssh://**************/eclipse/jaf.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/jaf</url>
      <tag>HEAD</tag>
  </scm>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/eclipse-ee4j/jaf/issues/</url>
    </issueManagement>

    <licenses>
        <license>
            <name>EDL 1.0</name>
            <url>http://www.eclipse.org/org/documents/edl-v10.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <properties>
	<activation.spec.version>1.2</activation.spec.version>
	<!-- defaults that are overridden in activation module -->
	<activation.extensionName>
	    ${project.groupId}.${project.artifactId}
	</activation.extensionName>
	<activation.specificationTitle>
	    ${project.groupId}.${project.artifactId}
	</activation.specificationTitle>
	<activation.implementationTitle>
	    ${project.groupId}.${project.artifactId}
	</activation.implementationTitle>
	<activation.bundle.symbolicName>
	    ${project.groupId}.${project.artifactId}
	</activation.bundle.symbolicName>
	<activation.bundle.symbolicName>
	    ${project.groupId}.${project.artifactId}
	</activation.bundle.symbolicName>
	<activation.packages.export>
	    javax.activation.*; version=${activation.spec.version}
	</activation.packages.export>
	<activation.packages.import>
	    *
	</activation.packages.import>
	<activation.packages.private>
	    com.sun.activation.*
	</activation.packages.private>
	<!-- for the osgiversion-maven-plugin -->
	<hk2.plugin.version>2.0.0</hk2.plugin.version>
	<project.build.sourceEncoding>iso-8859-1</project.build.sourceEncoding>
	<findbugs.threshold>
	    High
	</findbugs.threshold>
	<findbugs.version>
	    3.0.1
	</findbugs.version>
	<findbugs.skip>
	    true
	</findbugs.skip>
	<findbugs.exclude/>
        <copyright-plugin.version>2.2</copyright-plugin.version>
    </properties>

    <developers>
	<developer>
	    <id>shannon</id>
	    <name>Bill Shannon</name>
	    <email><EMAIL></email>
	    <organization>Oracle</organization>
	    <roles>
		<role>lead</role>
	    </roles>
	</developer>
    </developers>

    <!-- following to enable use of "mvn site:stage" -->
    <distributionManagement>
	<site>
	    <id>oracle.com</id>
	    <url>file:/tmp</url> <!-- not used -->
	</site>
    </distributionManagement>

    <modules>
	<module>activation</module>
	<module>activationapi</module>
    </modules>

    <profiles>
	<!--
	    This profile contains modules that should only be built
	    but not installed or deployed.
	-->
	<profile>
	    <id>build-only</id>
	    <modules>
		<module>demo</module>
	    </modules>
	    <activation>
		<activeByDefault>true</activeByDefault>
	    </activation>
	</profile>

	<!--
	    This profile is used for deploying a Jakarta Activation
	    final release.

	    Activating this profile manually for deployment causes
	    the above profile to be deactivated, which works around
	    an apparent bug in maven that prevents me from manually
	    deactivating a profile.  This profile purposely has none
	    of the modules I don't want to be deployed.
	-->
	<profile>
	    <id>oss-release</id>
	</profile>
    </profiles>

    <build>
	<defaultGoal>install</defaultGoal>
	<plugins>
	    <!--
		Make sure we're using the correct version of maven.
	    -->
	    <plugin>
		<groupId>org.apache.maven.plugins</groupId>
		<artifactId>maven-enforcer-plugin</artifactId>
		<executions>
		    <execution>
			<id>enforce-version</id>
			<goals>
			    <goal>enforce</goal>
			</goals>
			<configuration>
			    <rules>
				<requireMavenVersion>
				    <version>[3.0.3,)</version>
				</requireMavenVersion>
				<requireJavaVersion>
				    <version>[9,)</version>
				</requireJavaVersion>
			    </rules>
			</configuration>
		    </execution>
		</executions>
	    </plugin>

	    <!--
		This plugin is reponsible for packaging artifacts
		as OSGi bundles.  Please refer to
		http://felix.apache.org/site/apache-felix-maven-bundle-plugin-bnd.html
		for more information about how to use this plugin.
	    -->
	    <plugin>
		<groupId>org.apache.felix</groupId>
		<artifactId>maven-bundle-plugin</artifactId>
		<configuration>
		    <instructions>
			<Bundle-SymbolicName>
			    ${activation.bundle.symbolicName}
			</Bundle-SymbolicName>
			<Export-Package>
			    ${activation.packages.export}
			</Export-Package>
			<Import-Package>
			    ${activation.packages.import}
			</Import-Package>
			<Private-Package>
			    ${activation.packages.private}
			</Private-Package>
			<DynamicImport-Package>
			    *
			</DynamicImport-Package>
		    </instructions>
		</configuration>
		<!--
		    Since we don't change the packaging type to bundle, we
		    need to configure the plugin to execute the manifest goal
		    during the process-classes phase of the build life cycle.
		-->
		<executions>
		    <execution>
			<id>osgi-manifest</id>
			<phase>process-classes</phase>
			<goals>
			    <goal>manifest</goal>
			</goals>
		    </execution>
		</executions>
	    </plugin>

	    <!--
		Since we don't want a qualifier like b05 or SNAPSHOT to
		appear in the OSGi package version attribute, we use
		the following plugin to populate a project property
		with an OSGi version that is equivalent to the maven
		version without the qualifier.
	    -->
	    <plugin>
		<groupId>org.glassfish.hk2</groupId>
		<artifactId>osgiversion-maven-plugin</artifactId>
		<version>${hk2.plugin.version}</version>
		<configuration>
		    <dropVersionComponent>qualifier</dropVersionComponent>
		    <versionPropertyName>activation.osgiversion</versionPropertyName>
		</configuration>
		<executions>
		    <execution>
			<id>compute-osgi-version</id>
			<goals>
			    <goal>compute-osgi-version</goal>
			</goals>
		    </execution>
		</executions>
	    </plugin>

	    <!--
		Use the JDK 9+ compiler but with -source 1.8 for all
		but the module-info.java file.
	    -->
	    <plugin>
		<artifactId>maven-compiler-plugin</artifactId>
		<executions>
		    <execution>
			<id>default-compile</id>
			<configuration>
			    <source>1.8</source>
			    <target>1.8</target>
			    <excludes>
				<exclude>module-info.java</exclude>
			    </excludes>
			</configuration>
		    </execution>
		    <execution>
			<id>module-info-compile</id>
			<goals>
			    <goal>compile</goal>
			</goals>
			<configuration>
			    <release>9</release>
			    <includes>
				<include>module-info.java</include>
			    </includes>
			</configuration>
		    </execution>
		    <execution>
			<id>default-testCompile</id>
			<configuration>
			    <release>9</release>
			</configuration>
		    </execution>
		</executions>
	    </plugin>

	    <plugin>
		<artifactId>maven-jar-plugin</artifactId>
		<!-- need at least this version to make excludes work -->
		<configuration>
		    <finalName>${project.artifactId}</finalName>
		    <archive>
			<!--
			    Configure the maven-jar-plugin to pick up
			    META-INF/MANIFEST.MF that's generated by
			    the maven-bundle-plugin.
			-->
			<manifestFile>
			  ${project.build.outputDirectory}/META-INF/MANIFEST.MF
			</manifestFile>
			<manifestEntries>
			    <Extension-Name>
				${activation.extensionName}
			    </Extension-Name>
			    <Specification-Title>
				${activation.specificationTitle}
			    </Specification-Title>
			    <Specification-Version>
				${activation.spec.version}
			    </Specification-Version>
			    <Specification-Vendor>
				${project.organization.name}
			    </Specification-Vendor>
			    <Implementation-Title>
				${activation.implementationTitle}
			    </Implementation-Title>
			    <Implementation-Version>
				${project.version}
			    </Implementation-Version>
			    <Implementation-Vendor>
				${project.organization.name}
			    </Implementation-Vendor>
			    <Implementation-Vendor-Id>
				com.sun
			    </Implementation-Vendor-Id>
			</manifestEntries>
		    </archive>
		    <excludes>
			<exclude>**/*.java</exclude>
		    </excludes>
		</configuration>
	    </plugin>

	    <!--
		Tell the source plugin about the sources that may have
		been downloaded by the maven-dependency-plugin.
	    -->

	    <plugin>
		<groupId>org.codehaus.mojo</groupId>
		<artifactId>build-helper-maven-plugin</artifactId>
		<executions>
		    <execution>
			<id>add-source</id>
			<phase>generate-sources</phase>
			<goals>
			    <goal>add-source</goal>
			</goals>
			<configuration>
			    <sources>
				<source> <!-- for dependencies -->
				    ${project.build.directory}/sources
				</source>
			    </sources>
			</configuration>
		    </execution>
		    <execution>
			<id>add-resource</id>
			<phase>generate-resources</phase>
			<goals>
			    <goal>add-resource</goal>
			</goals>
			<configuration>
			    <resources>
				<resource>
				    <directory>${main.basedir}</directory>
				    <targetPath>META-INF</targetPath>
				    <includes>
					<include>LICENSE.md</include>
					<include>NOTICE.md</include>
				    </includes>
				</resource>
			    </resources>
			</configuration>
		    </execution>
		</executions>
	    </plugin>

	    <!--
		Directory plugin to find parent root directory absolute path.
		Sets ${main.basedir}, used above with build-helper-maven-plugin
		to include LICENSE.md and NOTICE.md in jar files.
	    -->
	    <plugin>
		<groupId>org.commonjava.maven.plugins</groupId>
		<artifactId>directory-maven-plugin</artifactId>
		<executions>
		    <execution>
			<id>directories</id>
			<goals>
			    <goal>highest-basedir</goal>
			</goals>
			<phase>initialize</phase>
			<configuration>
			    <property>main.basedir</property>
			</configuration>
		    </execution>
		</executions>
	    </plugin>

	    <!--
		Configure the source plugin here so that it will know
		about the sources that may have been downloaded by the
		maven-dependency-plugin and configured by the
		build-helper-maven-plugin.
	    -->
	    <plugin>
		<groupId>org.apache.maven.plugins</groupId>
		<artifactId>maven-source-plugin</artifactId>
		<executions>
		    <execution>
			<id>attach-sources</id>
			<goals>
			    <goal>jar-no-fork</goal> 
			</goals>
		    </execution>
		</executions>
		<configuration>
		    <includePom>true</includePom>
		    <!--
			Since we added the classes directory using the
			build-helper-maven-plugin above, we need to exclude
			the class files from the source jar file.
		    -->
		    <excludes>
			<exclude>**/*.class</exclude>
		    </excludes>
		</configuration>
	    </plugin>
	</plugins>

	<pluginManagement>
	    <plugins>
		<plugin>
		    <groupId>org.apache.maven.plugins</groupId>
		    <artifactId>maven-compiler-plugin</artifactId>
		    <version>3.7.0</version>
		</plugin>
		<plugin>
		    <groupId>org.apache.maven.plugins</groupId>
		    <artifactId>maven-surefire-plugin</artifactId>
		    <version>2.4.3</version>
		</plugin>
		<plugin>
		    <groupId>org.apache.maven.plugins</groupId>
		    <artifactId>maven-jar-plugin</artifactId>
		    <version>2.4</version>
		</plugin>
		<plugin>
		    <groupId>org.codehaus.mojo</groupId>
		    <artifactId>build-helper-maven-plugin</artifactId>
		    <version>1.7</version>
		</plugin>
		<plugin>
		    <groupId>org.commonjava.maven.plugins</groupId>
		    <artifactId>directory-maven-plugin</artifactId>
		    <version>0.3</version>
		</plugin>
		<plugin>
		    <groupId>org.apache.maven.plugins</groupId>
		    <artifactId>maven-assembly-plugin</artifactId>
		    <version>2.4</version>
		</plugin>
		<plugin>
		    <!--
			By default, disable the FindBugs plugin for all modules.
			It's enabled in the modules where we actually want to
			run it.
		    -->
		    <groupId>org.codehaus.mojo</groupId>
		    <artifactId>findbugs-maven-plugin</artifactId>
		    <version>${findbugs.version}</version>
		    <configuration>
			<skip>${findbugs.skip}</skip>
			<threshold>${findbugs.threshold}</threshold>
			<findbugsXmlWithMessages>true</findbugsXmlWithMessages>
			<excludeFilterFile>
			    ${findbugs.exclude}
			</excludeFilterFile>
		    </configuration>
		</plugin>
		<plugin>
		    <groupId>org.apache.maven.plugins</groupId>
		    <artifactId>maven-enforcer-plugin</artifactId>
		    <version>1.0</version>
		</plugin>
		<plugin>
		    <groupId>org.apache.felix</groupId>
		    <artifactId>maven-bundle-plugin</artifactId>
		    <version>3.5.0</version>
		</plugin>
		<plugin>
		    <groupId>org.apache.maven.plugins</groupId>
		    <artifactId>maven-source-plugin</artifactId>
		    <version>2.1.2</version>
		 </plugin>
		<plugin>
		    <groupId>org.apache.maven.plugins</groupId>
		    <artifactId>maven-javadoc-plugin</artifactId>
		    <version>3.1.1</version>
		</plugin>
		<plugin>
		    <groupId>org.apache.maven.plugins</groupId>
		    <artifactId>maven-project-info-reports-plugin</artifactId>
		    <version>2.7</version>
		</plugin>
		<plugin>
		    <groupId>org.glassfish.copyright</groupId>
		    <artifactId>glassfish-copyright-maven-plugin</artifactId>
		    <version>${copyright-plugin.version}</version>
		    <configuration>
			<scm>git</scm>
			<scmOnly>true</scmOnly> 
			<excludeFile>
			    copyright-exclude
			</excludeFile>
		    </configuration>
		</plugin>
	    </plugins>
	</pluginManagement>
    </build>

    <dependencyManagement>
	<dependencies>
	    <dependency>
		<groupId>com.sun.activation</groupId>
		<artifactId>jakarta.activation</artifactId>
		<version>${project.version}</version>
	    </dependency>
	</dependencies>
    </dependencyManagement>

    <reporting>
	<plugins>
	    <!--
		Configure FindBugs to run with "mvn site" and
		generate html output that can be viewed directly.
	    -->
	    <plugin>
		<groupId>org.codehaus.mojo</groupId>
		<artifactId>findbugs-maven-plugin</artifactId>
		<version>${findbugs.version}</version>
		<configuration>
		    <skip>${findbugs.skip}</skip>
		    <threshold>${findbugs.threshold}</threshold>
		    <excludeFilterFile>
			${findbugs.exclude}
		    </excludeFilterFile>
		</configuration>
	    </plugin>
	</plugins>
    </reporting>
</project>
