<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.ini4j</groupId>
    <artifactId>ini4j</artifactId>
    <version>0.5.4</version>
    <name>ini4j</name>
    <description>Java API for handling configuration files in Windows .ini format. The library includes its own Map based API, Java Preferences API and Java Beans API for handling .ini files. Additionally, the library includes a feature rich (variable/macro substitution, multiply property values, etc) java.util.Properties replacement.</description>
    <packaging>jar</packaging>
    <url>http://www.ini4j.org</url>
    <scm>
        <connection>scm:svn:https://svn.code.sf.net/p/ini4j/code/tags/ini4j-0.5.4/</connection>
        <developerConnection>scm:svn:svn+ssh://<EMAIL>/p/ini4j/code/tags/ini4j-0.5.4</developerConnection>
        <url>https://svn.code.sf.net/p/ini4j/code/tags/ini4j-0.5.4/</url>
    </scm>
    <distributionManagement>
        <repository>
            <id>ini4j-release</id>
            <name>[ini4j] releases on OSSRH</name>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
        </repository>
        <snapshotRepository>
            <id>ini4j-snapshot</id>
            <name>[ini4j] snapshots on OSSRH</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <site>
            <id>ini4j-site</id>
            <name>[ini4j] site on SourceForge</name>
            <url>scpexe://shell.sourceforge.net/home/<USER>/ini4j/htdocs</url>
        </site>
    </distributionManagement>
    <issueManagement>
        <system>sourceforge</system>
        <url>http://sourceforge.net/tracker2/?group_id=129580</url>
    </issueManagement>
    <licenses>
        <license>
            <name>Apache 2</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <developers>
        <developer>
            <id>ivan</id>
            <name>Ivan Szkiba</name>
            <email>szkiba at SourceForge</email>
            <roles>
                <role>architect</role>
                <role>developer</role>
            </roles>
            <timezone>-1</timezone>
        </developer>
    </developers>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.5</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>jetty</groupId>
            <artifactId>jetty</artifactId>
            <version>4.2.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <version>2.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymockclassextension</artifactId>
            <version>2.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.beanshell</groupId>
            <artifactId>bsh</artifactId>
            <version>2.0b4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.sf.retrotranslator</groupId>
            <artifactId>retrotranslator-runtime</artifactId>
            <version>1.2.4</version>
            <optional>true</optional>
        </dependency>
    </dependencies>
    <properties>
        <upload.url>scpexe://shell.sourceforge.net/home/<USER>/project/ini4j</upload.url>
        <check.skip>true</check.skip>
        <!-- <jdk14.home>/usr/local/j2sdk1.4.2_19</jdk14.home> -->
    </properties>
    <profiles>
        <profile>
            <id>windows</id>
            <activation>
                <os>
                    <family>windows</family>
                </os>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <systemProperties>
                                <property>
                                    <name>os.family</name>
                                    <value>windows</value>
                                </property>
                            </systemProperties>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-candidate</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <version>2.3</version>
                        <configuration>
                            <configLocation>${basedir}/src/conf/checkstyle.xml</configLocation>
                            <failOnViolation>true</failOnViolation>
                            <consoleOutput>true</consoleOutput>
                            <encoding>UTF-8</encoding>
                            <skip>${check.skip}</skip>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-pmd-plugin</artifactId>
                        <dependencies>
                            <dependency>
                                <groupId>pmd</groupId>
                                <artifactId>pmd-jdk14</artifactId>
                                <version>4.2.5</version>
                            </dependency>
                        </dependencies>
                        <configuration>
                            <failOnViolation>true</failOnViolation>
                            <rulesets>
                                <ruleset>${basedir}/src/conf/pmd.xml</ruleset>
                            </rulesets>
                            <skip>${check.skip}</skip>
                            <verbose>true</verbose>
                            <sourceEncoding>UTF-8</sourceEncoding>
                            <targetJdk>1.5</targetJdk>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy-junit</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>unpack</goal>
                                </goals>
                                <configuration>
                                    <artifactItems>
                                        <artifactItem>
                                            <groupId>junit</groupId>
                                            <artifactId>junit</artifactId>
                                            <type>jar</type>
                                            <overWrite>true</overWrite>
                                        </artifactItem>
                                        <artifactItem>
                                            <groupId>org.easymock</groupId>
                                            <artifactId>easymock</artifactId>
                                            <type>jar</type>
                                            <overWrite>true</overWrite>
                                        </artifactItem>
                                    </artifactItems>
                                    <outputDirectory>${project.build.directory}/test-deps</outputDirectory>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>retrotranslator-maven-plugin</artifactId>
                        <dependencies>
                            <dependency>
                                <groupId>net.sf.retrotranslator</groupId>
                                <artifactId>retrotranslator-runtime</artifactId>
                                <version>1.2.4</version>
                            </dependency>
                        </dependencies>
                        <configuration>
                            <embed>org.ini4j.jdk14</embed>
                            <advanced>true</advanced>
                        </configuration>
                        <executions>
                            <execution>
                                <id>translate-classes</id>
                                <goals>
                                    <goal>translate</goal>
                                </goals>
                                <phase>process-classes</phase>
                                <configuration>
                                    <destdir>${project.build.directory}/classes-jdk14</destdir>
                                    <filesets>
                                        <fileset>
                                            <directory>${project.build.outputDirectory}</directory>
                                        </fileset>
                                    </filesets>
                                    <embed>org.ini4j.jdk14</embed>
                                    <advanced>true</advanced>
                                </configuration>
                            </execution>
                            <execution>
                                <id>translate-test-classes</id>
                                <goals>
                                    <goal>translate</goal>
                                </goals>
                                <phase>process-test-classes</phase>
                                <configuration>
                                    <destdir>${project.build.directory}/test-classes-jdk14</destdir>
                                    <filesets>
                                        <fileset>
                                            <directory>${project.build.testOutputDirectory}</directory>
                                        </fileset>
                                        <fileset>
                                            <directory>${project.build.directory}/test-deps</directory>
                                        </fileset>
                                    </filesets>
                                    <embed>org.ini4j.jdk14</embed>
                                    <advanced>true</advanced>
                                </configuration>
                            </execution>
                            <execution>
                                <id>translate-project</id>
                                <goals>
                                    <goal>translate-project</goal>
                                </goals>
                                <configuration>
                                    <classifier>jdk14</classifier>
                                    <attach>true</attach>
                                    <advanced>true</advanced>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <dependencies>
                            <dependency>
                                <groupId>org.apache.ant</groupId>
                                <artifactId>ant-junit</artifactId>
                                <version>1.7.0</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>test</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <tasks if="jdk14.home" unless="skip_jdk14_tests">
                                        <junit printsummary="on" haltonfailure="true" showoutput="true" fork="yes" forkMode="perBatch" jvm="${jdk14.home}/bin/java">
                                            <sysproperty key="java.util.prefs.PreferencesFactory" value="org.ini4j.IniPreferencesFactory" />
                                            <sysproperty key="org.ini4j.prefs.user" value="org/ini4j/sample/dwarfs.ini" />
                                            <sysproperty key="org.ini4j.prefs.system" value="org/ini4j/sample/dwarfs.ini" />
                                            <sysproperty key="basedir" value="${basedir}" />
                                            <classpath>
                                                <pathelement location="${basedir}/target/test-classes-jdk14" />
                                                <pathelement location="${basedir}/target/classes-jdk14" />
                                            </classpath>
                                            <formatter type="plain" usefile="false" />
                                            <batchtest>
                                                <fileset dir="${basedir}/src/test/java">
                                                    <include name="**/*Test.java" />
                                                    <exclude name="**/SampleRunnerTest.java" />
                                                </fileset>
                                            </batchtest>
                                        </junit>
                                    </tasks>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadoc</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                </plugins>
            </build>
        </profile>
    </profiles>
    <build>
        <extensions>
            <!-- Enabling the use of SSH -->
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-ssh-external</artifactId>
                <version>1.0-beta-6</version>
            </extension>
        </extensions>		
        <plugins>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>1.6.3</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>ini4j-release</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                </configuration>
            </plugin>            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-gpg-plugin</artifactId>
                <version>1.5</version>
                <executions>
                    <execution>
                        <id>sign-artifacts</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>sign</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-changes-plugin</artifactId>
                <version>2.1</version>
                <inherited>false</inherited>
                <configuration>
                    <templateDirectory>src/changes</templateDirectory>
                    <templateEncoding>UTF-8</templateEncoding>
                    <outputDirectory>${project.build.directory}</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>releasenotes</id>
                        <goals>
                            <goal>announcement-generate</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <template>ReleaseNotes.vm</template>
                        </configuration>
                    </execution>
                    <execution>
                        <id>changelog</id>
                        <goals>
                            <goal>announcement-generate</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <template>ChangeLog.vm</template>
                        </configuration>
                    </execution>
                    <execution>
                        <id>sitemap-create</id>
                        <goals>
                            <goal>announcement-generate</goal>
                        </goals>
                        <phase>pre-site</phase>
                        <configuration>
                            <template>sitemap.vm</template>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>sitemap-copy</id>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <phase>pre-site</phase>
                        <configuration>
                            <tasks>
                                <property name="dir" value="${basedir}/target/generated-site/resources" />
                                <mkdir dir="${dir}" />
                                <copy file="target/sitemap.vm" tofile="${dir}/sitemap.xml" />
                                <gzip src="${dir}/sitemap.xml" destfile="${dir}/sitemap.xml.gz" />
                            </tasks>
                        </configuration>
                    </execution>
                    <execution>
                        <id>release-notes-changelog-copy</id>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <tasks>
                                <property name="dir" value="${basedir}/target" />
                                <copy file="target/ReleaseNotes.vm" tofile="target/ReleaseNotes.txt" />
                                <copy file="target/ChangeLog.vm" tofile="target/ChangeLog.txt" />
                            </tasks>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.5</source>
                    <target>1.5</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgument>-Xlint</compilerArgument>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <systemProperties>
                        <property>
                            <name>java.util.prefs.PreferencesFactory</name>
                            <value>org.ini4j.IniPreferencesFactory</value>
                        </property>
                        <property>
                            <name>org.ini4j.prefs.user</name>
                            <value>org/ini4j/sample/dwarfs.ini</value>
                        </property>
                        <property>
                            <name>org.ini4j.prefs.system</name>
                            <value>org/ini4j/sample/dwarfs.ini</value>
                        </property>
                    </systemProperties>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>zip</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptors>
                                <descriptor>src/main/assembly/src.xml</descriptor>
                                <descriptor>src/main/assembly/bin.xml</descriptor>
                                <descriptor>src/main/assembly/all.xml</descriptor>
                            </descriptors>
                            <attach>false</attach>
                        </configuration>
                    </execution>
                    <execution>
                        <id>demo</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/generated-site/resources</outputDirectory>
                            <finalName>ini4j-demo</finalName>
                            <appendAssemblyId>false</appendAssemblyId>
                            <descriptors>
                                <descriptor>src/main/assembly/demo.xml</descriptor>
                            </descriptors>
                            <attach>false</attach>
                            <archive>
                                <manifest>
                                    <mainClass>org.ini4j.demo.DemoMain</mainClass>
                                </manifest>
                            </archive>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>wagon-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>upload-all</id>
                        <goals>
                            <goal>upload</goal>
                        </goals>
                        <phase>deploy</phase>
                        <configuration>
                            <serverId>ini4j-upload</serverId>
                            <fromDir>${project.build.directory}</fromDir>
                            <includes>${project.build.finalName}-all.zip,ReleaseNotest.txt</includes>
                            <url>${upload.url}/ini4j/${project.version}</url>
                        </configuration>
                    </execution>
                    <execution>
                        <id>upload-src</id>
                        <goals>
                            <goal>upload</goal>
                        </goals>
                        <phase>deploy</phase>
                        <configuration>
                            <serverId>ini4j-upload</serverId>
                            <fromDir>${project.build.directory}</fromDir>
                            <includes>${project.build.finalName}-src.zip,ReleaseNotest.txt</includes>
                            <url>${upload.url}/ini4j-src/${project.version}</url>
                        </configuration>
                    </execution>
                    <execution>
                        <id>upload-bin</id>
                        <goals>
                            <goal>upload</goal>
                        </goals>
                        <phase>deploy</phase>
                        <configuration>
                            <serverId>ini4j-upload</serverId>
                            <fromDir>${project.build.directory}</fromDir>
                            <includes>${project.build.finalName}-bin.zip,ReleaseNotest.txt</includes>
                            <url>${upload.url}/ini4j-bin/${project.version}</url>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>2.0</version>
                <configuration>
                    <generatedSiteDirectory>${basedir}/target/generated-site</generatedSiteDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.google.code.maven-license-plugin</groupId>
                <artifactId>maven-license-plugin</artifactId>
                <version>1.4.0</version>
                <inherited>true</inherited>
                <configuration>
                    <aggregate>true</aggregate>
                    <encoding>UTF-8</encoding>
                    <useDefaultExcludes>true</useDefaultExcludes>
                    <header>NOTICE.txt</header>
                    <excludes>
                        <exclude>**/META-INF/services/**</exclude>
                        <exclude>**/*.txt</exclude>
                        <exclude>**/*.apt</exclude>
                        <exclude>**/*.reg</exclude>
                        <exclude>**/.htaccess</exclude>
                        <exclude>**/UTF*.ini</exclude>
                    </excludes>
                    <mapping>
                        <java>SLASHSTAR_STYLE</java>
                        <css>SLASHSTAR_STYLE</css>
                        <js>SLASHSTAR_STYLE</js>
                        <ini>SEMICOLON_STYLE</ini>
                        <opt>SCRIPT_STYLE</opt>
                    </mapping>
                    <useDefaultMapping>true</useDefaultMapping>
                </configuration>
                <executions>
                    <execution>
                        <phase>initialize</phase>
                        <goals>
                            <goal>format</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>


            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>org.beanshell</groupId>
                                    <artifactId>bsh</artifactId>
                                    <type>jar</type>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/test-classes</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


        </plugins>
        <testResources>
            <testResource>
                <directory>src/test/java</directory>
                <includes>
                    <include>**/*.ini</include>
                    <include>**/*.opt</include>
                    <include>**/*.reg</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.txt</include>
                </includes>
            </testResource>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <configuration>
                    <inputEncoding>UTF-8</inputEncoding>
                    <linkJavadoc>true</linkJavadoc>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <charset>UTF-8</charset>
                    <encoding>ISO-8859-1</encoding>
                    <quiet>true</quiet>
                    <noqualifier>java.*</noqualifier>
                    <links>
                        <link>http://java.sun.com/javase/6/docs/api/</link>
                    </links>
                    <keywords>true</keywords>
                </configuration>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>javadoc</report>
                        </reports>
                    </reportSet>
                </reportSets>

            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>taglist-maven-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <tags>
                        <tag>TODO</tag>
                        <tag>FIXME</tag>
                        <tag>XXX</tag>
                    </tags>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-changes-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <templateEncoding>UTF-8</templateEncoding>
                    <issueLinkTemplate>http://sourceforge.net/tracker2/?func=detail&amp;atid=715133&amp;group_id=129580&amp;aid=%ISSUE%</issueLinkTemplate>
                </configuration>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>changes-report</report>
                        </reports>
                    </reportSet>
                </reportSets>

            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>2.1.2</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>index</report>
                            <report>summary</report>
                            <report>issue-tracking</report>
                            <report>license</report>
                            <report>project-team</report>
                            <report>dependencies</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>

        </plugins>
    </reporting>

</project>

