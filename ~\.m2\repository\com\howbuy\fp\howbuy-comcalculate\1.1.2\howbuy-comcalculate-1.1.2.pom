<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.howbuy.fp</groupId>
  <artifactId>howbuy-comcalculate</artifactId>
  <version>1.1.2</version>
  
   <build>
   <finalName>howbuy-comcalculate-${project.version}</finalName>
   		<plugins>
   			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>1.6</source>
					<target>1.6</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>

   		</plugins>
</build>
 <distributionManagement>
 
<repository>
   <id>releases</id>
     <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases</url>
       </repository>
        <snapshotRepository>
         <id>snapshots</id>
           <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots</url>
             </snapshotRepository>
               </distributionManagement>  		
     
</project>
