<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Hibernate Validator, declare and validate application constraints
  ~
  ~ License: Apache License, Version 2.0
  ~ See the license.txt file in the root directory or <http://www.apache.org/licenses/LICENSE-2.0>.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator-relocation</artifactId>
        <version>6.0.14.Final</version>
    </parent>

    <groupId>org.hibernate</groupId>
    <artifactId>hibernate-validator</artifactId>
    <name>Hibernate Validator Engine - Relocation Artifact</name>

    <distributionManagement>
        <relocation>
            <groupId>org.hibernate.validator</groupId>
        </relocation>
    </distributionManagement>
</project>
