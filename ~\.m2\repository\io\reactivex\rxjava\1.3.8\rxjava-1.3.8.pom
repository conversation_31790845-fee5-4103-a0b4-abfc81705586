<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.reactivex</groupId>
  <artifactId>rxjava</artifactId>
  <version>1.3.8</version>
  <name>rxjava</name>
  <description>rxjava</description>
  <developers>
    <developer>
      <id>benj<PERSON><PERSON>ensen</id>
      <name><PERSON></name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <properties>
    <nebula_Manifest_Version>1.0</nebula_Manifest_Version>
    <nebula_Implementation_Title>io.reactivex#rxjava;1.3.8</nebula_Implementation_Title>
    <nebula_Implementation_Version>1.3.8</nebula_Implementation_Version>
    <nebula_Built_Status>integration</nebula_Built_Status>
    <nebula_Built_By>travis</nebula_Built_By>
    <nebula_Built_OS>Linux</nebula_Built_OS>
    <nebula_Build_Date>2018-03-31_15:30:39</nebula_Build_Date>
    <nebula_Gradle_Version>2.14</nebula_Gradle_Version>
    <nebula_Module_Owner><EMAIL></nebula_Module_Owner>
    <nebula_Module_Email><EMAIL></nebula_Module_Email>
    <nebula_Module_Source></nebula_Module_Source>
    <nebula_Module_Origin>https://github.com/ReactiveX/RxJava.git</nebula_Module_Origin>
    <nebula_Change>7e3879a</nebula_Change>
    <nebula_Branch>7e3879abfb32eeebb38c970195a7f1e354eb1f82</nebula_Branch>
    <nebula_Build_Host>travis-job-7634ec5f-11ac-4d0f-ac92-2fe172437739</nebula_Build_Host>
    <nebula_Build_Job>LOCAL</nebula_Build_Job>
    <nebula_Build_Number>LOCAL</nebula_Build_Number>
    <nebula_Build_Id>LOCAL</nebula_Build_Id>
    <nebula_Created_By>1.8.0_151-b12 (Oracle Corporation)</nebula_Created_By>
    <nebula_Build_Java_Version>1.8.0_151</nebula_Build_Java_Version>
    <nebula_X_Compile_Target_JDK>1.6</nebula_X_Compile_Target_JDK>
    <nebula_X_Compile_Source_JDK>1.6</nebula_X_Compile_Source_JDK>
  </properties>
  <url>https://github.com/ReactiveX/RxJava</url>
  <scm>
    <url>https://github.com/ReactiveX/RxJava.git</url>
  </scm>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
</project>
