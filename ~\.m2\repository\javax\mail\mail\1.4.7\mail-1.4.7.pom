<?xml version="1.0" encoding="iso-8859-1"?>
<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 1997-2011 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    https://glassfish.dev.java.net/public/CDDL+GPL_1_1.html
    or packager/legal/LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at packager/legal/LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<!--
    This project creates a jar file using the old name (mail.jar),
    the old groupId:artifactId, etc.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
			    http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
	<groupId>com.sun.mail</groupId>
	<artifactId>all</artifactId>
	<version>1.4.7</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>javax.mail</groupId>
    <artifactId>mail</artifactId>
    <packaging>jar</packaging>
    <name>JavaMail API (compat)</name>

    <properties>
	<!-- override the OSGi Bundle-SymbolicName -->
	<mail.bundle.symbolicName>
	    javax.mail
	</mail.bundle.symbolicName>
	<!-- rest copied from mail/pom.xml -->
	<mail.extensionName>
	    javax.mail
	</mail.extensionName>
	<mail.specificationTitle>
	    JavaMail(TM) API Design Specification
	</mail.specificationTitle>
	<mail.implementationTitle>
	    javax.mail
	</mail.implementationTitle>
	<mail.packages.export>
	    javax.mail.*; version=${mail.spec.version},
	    com.sun.mail.imap; version=${mail.osgiversion},
	    com.sun.mail.imap.protocol; version=${mail.osgiversion},
	    com.sun.mail.iap; version=${mail.osgiversion},
	    com.sun.mail.pop3; version=${mail.osgiversion},
	    com.sun.mail.smtp; version=${mail.osgiversion},
	    com.sun.mail.util; version=${mail.osgiversion},
	    com.sun.mail.util.logging; version=${mail.osgiversion},
	    com.sun.mail.handlers; version=${mail.osgiversion}
	</mail.packages.export>
	<mail.probeFile>
	    META-INF/gfprobe-provider.xml
	</mail.probeFile>
    </properties>

    <build>
        <plugins>
	    <plugin>
		<artifactId>maven-dependency-plugin</artifactId>
		<executions>
		    <execution>
			<!-- download the binaries -->
			<id>get-binaries</id>
			<phase>process-sources</phase>
			<goals>
			    <goal>unpack</goal>
			</goals>
		    </execution>
		    <execution>
			<!-- download the sources -->
			<id>get-sources</id>
			<phase>process-sources</phase>
			<goals>
			    <goal>unpack</goal>
			</goals>
			<configuration>
			    <artifactItems>
				<artifactItem>
				    <groupId>com.sun.mail</groupId>
				    <artifactId>javax.mail</artifactId>
				    <version>${mail.version}</version>
				    <classifier>sources</classifier>
				    <outputDirectory>
					${project.build.directory}/sources
				    </outputDirectory>
				</artifactItem>
			    </artifactItems>
			</configuration>
		    </execution>
		</executions>
		<configuration>
		    <artifactItems>
			<artifactItem>
			    <groupId>com.sun.mail</groupId>
			    <artifactId>javax.mail</artifactId>
			    <version>${mail.version}</version>
			</artifactItem>
		    </artifactItems>
		    <outputDirectory>
			${project.build.outputDirectory}
		    </outputDirectory>
		    <excludes>
			META-INF/maven/**
		    </excludes>
		</configuration>
	    </plugin>
	    <plugin>
		<artifactId>maven-jar-plugin</artifactId>
		<configuration>
		    <finalName>${project.artifactId}</finalName>
		    <archive>
			<manifestFile>
			  ${project.build.outputDirectory}/META-INF/MANIFEST.MF
			</manifestFile>
		    </archive>
		</configuration>
	    </plugin>
        </plugins>
    </build>
</project>
