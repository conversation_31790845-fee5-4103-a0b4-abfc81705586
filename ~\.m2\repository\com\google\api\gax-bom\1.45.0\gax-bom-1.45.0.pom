<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.api</groupId>
  <artifactId>gax-bom</artifactId>
  <version>1.45.0</version><!-- {x-version-update:gax-bom:current} -->
  <packaging>pom</packaging>
  <name>GAX (Google Api eXtensions) for Java</name>
  <description>Google Api eXtensions for Java</description>
  <url>https://github.com/googleapis/gax-java</url>
  <licenses>
    <license>
      <name>BSD</name>
      <url>https://github.com/googleapis/gax-java/blob/master/LICENSE</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>GoogleAPIs</id>
      <name>GoogleAPIs</name>
      <email><EMAIL></email>
      <url>https://github.com/googleapis/gax-java</url>
      <organization>Google, LLC</organization>
      <organizationUrl>https://www.google.com</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/googleapis/gax-java.git</connection>
    <url>https://github.com/googleapis/gax-java</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax</artifactId>
        <version>1.45.0</version><!-- {x-version-update:gax:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax</artifactId>
        <version>1.45.0</version><!-- {x-version-update:gax:current} -->
	<classifier>testlib</classifier>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax-grpc</artifactId>
        <version>1.45.0</version><!-- {x-version-update:gax-grpc:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax-grpc</artifactId>
        <version>1.45.0</version><!-- {x-version-update:gax-grpc:current} -->
	<classifier>testlib</classifier>
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax-httpjson</artifactId>
        <version>0.62.0</version><!-- {x-version-update:gax-httpjson:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api</groupId>
        <artifactId>gax-httpjson</artifactId>
        <version>0.62.0</version><!-- {x-version-update:gax-httpjson:current} -->
	<classifier>testlib</classifier>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
