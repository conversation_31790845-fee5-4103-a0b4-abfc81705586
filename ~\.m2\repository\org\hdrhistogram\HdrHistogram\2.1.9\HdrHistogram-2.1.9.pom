<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>7</version>
    </parent>

    <groupId>org.hdrhistogram</groupId>
    <artifactId>HdrHistogram</artifactId>
    <version>2.1.9</version>

    <name>HdrHistogram</name>

    <url>http://hdrhistogram.github.io/HdrHistogram/</url>

    <description>
        HdrHistogram supports the recording and analyzing sampled data value
        counts across a configurable integer value range with configurable value
        precision within the range. Value precision is expressed as the number of
        significant digits in the value recording, and provides control over value
        quantization behavior across the value range and the subsequent value
        resolution at any given level.
    </description>

    <licenses>
        <license>
            <comments>
                * This code was Written by Gil Tene of Azul Systems, and released to the
                * public domain, as explained at http://creativecommons.org/publicdomain/zero/1.0/
            </comments>
            <name>Public Domain, per Creative Commons CC0</name>
            <url>http://creativecommons.org/publicdomain/zero/1.0/</url>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>giltene</id>
            <name>Gil Tene</name>
            <url>https://github.com/giltene</url>
        </developer>
    </developers>

    <scm>
        <url>scm:git:git://github.com/HdrHistogram/HdrHistogram.git</url>
        <connection>scm:git:git://github.com/HdrHistogram/HdrHistogram.git</connection>
        <developerConnection>scm:git:**************:HdrHistogram/HdrHistogram.git</developerConnection>
      <tag>HdrHistogram-2.0.2</tag>
    </scm>


    <issueManagement>
        <url>https://github.com/HdrHistogram/HdrHistogram/issues</url>
        <system>GitHub Issues</system>
    </issueManagement>

    <packaging>bundle</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <version.template.file>src/main/java/org/HdrHistogram/Version.java.template</version.template.file>
        <version.file>src/main/java/org/HdrHistogram/Version.java</version.file>
    </properties>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>2.5.3</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.4</version>
                <inherited>true</inherited>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9.1</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.12.4</version>
                <configuration>
                    <enableAssertions>false</enableAssertions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <arguments>-Dgpg.passphrase=${gpg.passphrase}</arguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.google.code.maven-replacer-plugin</groupId>
                <artifactId>maven-replacer-plugin</artifactId>
                <version>1.4.0</version>
                <executions>
                    <execution>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>replace</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <file>${version.template.file}</file>
                    <outputFile>${version.file}</outputFile>
                    <replacements>
                        <replacement>
                            <token>\$BUILD_TIME\$</token>
                            <value>${maven.build.timestamp}</value>
                        </replacement>
                        <replacement>
                            <token>\$VERSION\$</token>
                            <value>${project.version}</value>
                        </replacement>
                    </replacements>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.8</version>
                <executions>
                    <execution>
                        <id>copy-installed</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>${project.groupId}</groupId>
                                    <artifactId>${project.artifactId}</artifactId>
                                    <version>${project.version}</version>
                                    <type>${project.packaging}</type>
                                    <destFileName>HdrHistogram.jar</destFileName>
                                </artifactItem>
                            </artifactItems>
                            <outputDirectory>${project.basedir}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.4</version>
                        <configuration>
                            <passphrase>${gpg.passphrase}</passphrase>
                        </configuration>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.10</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
