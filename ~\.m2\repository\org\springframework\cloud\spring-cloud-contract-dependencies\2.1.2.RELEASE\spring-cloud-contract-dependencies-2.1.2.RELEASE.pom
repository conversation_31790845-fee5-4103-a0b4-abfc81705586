<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>spring-cloud-dependencies-parent</artifactId>
		<groupId>org.springframework.cloud</groupId>
		<version>2.1.6.RELEASE</version>
		<relativePath/>
	</parent>
	<artifactId>spring-cloud-contract-dependencies</artifactId>
	<version>2.1.2.RELEASE</version>
	<packaging>pom</packaging>
	<name>spring-cloud-contract-dependencies</name>
	<description>Spring Cloud Contract Dependencies</description>
	<properties>
		<wiremock.version>2.20.0</wiremock.version>
		<jsonassert.version>0.4.13</jsonassert.version>
		<rest-assured.version>3.2.0</rest-assured.version>
		<maven.version>3.3.9</maven.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-contract-wiremock</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-contract-spec</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-contract-verifier</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-contract-converters</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-contract-pact</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-contract-stub-runner</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-contract-stub-runner-boot</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-contract-shade</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-contract-verifier</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-contract-stub-runner</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-contract-stub-runner-jetty</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-contract-maven-plugin</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-contract-gradle-plugin</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.tomakehurst</groupId>
				<artifactId>wiremock-standalone</artifactId>
				<version>${wiremock.version}</version>
			</dependency>
			<dependency>
				<groupId>com.toomuchcoding.jsonassert</groupId>
				<artifactId>jsonassert</artifactId>
				<version>${jsonassert.version}</version>
			</dependency>
			<dependency>
				<groupId>io.rest-assured</groupId>
				<artifactId>spring-mock-mvc</artifactId>
				<version>${rest-assured.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>spring-web</artifactId>
						<groupId>org.springframework</groupId>
					</exclusion>
					<exclusion>
						<artifactId>spring-webmvc</artifactId>
						<groupId>org.springframework</groupId>
					</exclusion>
					<exclusion>
						<artifactId>spring-test</artifactId>
						<groupId>org.springframework</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>io.rest-assured</groupId>
				<artifactId>spring-web-test-client</artifactId>
				<version>${rest-assured.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>spring-context</artifactId>
						<groupId>org.springframework</groupId>
					</exclusion>
					<exclusion>
						<artifactId>spring-webflux</artifactId>
						<groupId>org.springframework</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>io.rest-assured</groupId>
				<artifactId>rest-assured</artifactId>
				<version>${rest-assured.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.maven</groupId>
				<artifactId>maven-settings-builder</artifactId>
				<version>${maven.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<profiles>
		<profile>
			<id>spring</id>
			<repositories>
				<repository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/snapshot</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</repository>
				<repository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
				<repository>
					<id>spring-releases</id>
					<name>Spring Releases</name>
					<url>https://repo.spring.io/release</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
			</repositories>
			<pluginRepositories>
				<pluginRepository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/snapshot</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</pluginRepository>
				<pluginRepository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</pluginRepository>
			</pluginRepositories>
		</profile>
	</profiles>
</project>
