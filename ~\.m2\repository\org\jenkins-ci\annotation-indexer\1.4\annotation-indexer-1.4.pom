<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.jenkins-ci</groupId>
    <artifactId>jenkins</artifactId>
    <version>1.26</version>
  </parent>

  <artifactId>annotation-indexer</artifactId>
  <name>Annotation Indexer</name>
  <version>1.4</version>
  <description>
    Creates index of annotations.
  </description>

  <dependencies>
    <dependency>
      <groupId>org.kohsuke.metainf-services</groupId>
      <artifactId>metainf-services</artifactId>
      <version>1.4</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.10</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.jolira</groupId>
      <artifactId>hickory</artifactId>
      <version>1.0.0</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <scm>
    <connection>scm:git:git://github.com/jenkinsci/lib-${project.artifactId}.git</connection>
    <developerConnection>scm:git:**************:jenkinsci/lib-${project.artifactId}.git</developerConnection>
  </scm>

  <licenses>
    <license>
      <name>MIT License</name>
      <url>http://jenkins-ci.org/mit-license</url>
    </license>
  </licenses>

</project>
