<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>crm-portrait</artifactId>
        <groupId>com.howbuy.crm</groupId>
        <version>2.0.0.5-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>crm-portrait-client</name>
    <artifactId>crm-portrait-client</artifactId>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <howbuy-commons-validator.version>1.0.0-SNAPSHOT</howbuy-commons-validator.version>
        <lobok.version>1.18.26</lobok.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lobok.version}</version>
        </dependency>

        <dependency>
            <artifactId>howbuy-commons-validator</artifactId>
            <groupId>com.howbuy.commons.validator</groupId>
            <version>${howbuy-commons-validator.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
    </dependencies>


</project>