<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2013-2018 the original author or authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>spring-cloud-dependencies-parent</artifactId>
		<groupId>org.springframework.cloud</groupId>
		<version>2.1.5.RELEASE</version>
		<relativePath/>
	</parent>
	<artifactId>spring-cloud-kubernetes-dependencies</artifactId>
	<version>1.0.2.RELEASE</version>
	<packaging>pom</packaging>
	<name>Spring Cloud Kubernetes :: Dependencies</name>
	<description>Spring Cloud Kubernetes Dependencies</description>
	<properties>
		<arquillian.version>1.4.0.Final</arquillian.version>
		<arquillian-cube.version>1.15.2</arquillian-cube.version>
		<kubernetes-client.version>4.1.0</kubernetes-client.version>
		<istio-client.version>1.0.0</istio-client.version>
		<mockwebserver.version>0.1.0</mockwebserver.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>io.fabric8</groupId>
				<artifactId>kubernetes-client-bom</artifactId>
				<version>${kubernetes-client.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>me.snowdrop</groupId>
				<artifactId>istio-client</artifactId>
				<version>${istio-client.version}</version>
			</dependency>

			<!-- Own dependencies -->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-kubernetes-core</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-kubernetes-config</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-kubernetes-discovery</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-kubernetes-istio</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-kubernetes-ribbon</artifactId>
				<version>${project.version}</version>
			</dependency>

			<!-- Own dependencies - Starters -->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-kubernetes</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-kubernetes-config</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-kubernetes-ribbon</artifactId>
				<version>${project.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-kubernetes-all</artifactId>
				<version>${project.version}</version>
			</dependency>

			<!-- Testing Dependencies -->
			<dependency>
				<groupId>org.jboss.arquillian.junit</groupId>
				<artifactId>arquillian-junit-standalone</artifactId>
				<version>${arquillian.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.arquillian.cube</groupId>
				<artifactId>arquillian-cube-requirement</artifactId>
				<version>${arquillian-cube.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.arquillian.cube</groupId>
				<artifactId>arquillian-cube-kubernetes</artifactId>
				<version>${arquillian-cube.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.arquillian.cube</groupId>
				<artifactId>arquillian-cube-openshift</artifactId>
				<version>${arquillian-cube.version}</version>
				<scope>test</scope>
			</dependency>

			<dependency>
				<groupId>io.fabric8</groupId>
				<artifactId>mockwebserver</artifactId>
				<version>${mockwebserver.version}</version>
				<scope>test</scope>
				<exclusions>
					<exclusion>
						<groupId>com.fasterxml.jackson.core</groupId>
						<artifactId>jackson-databind</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.fasterxml.jackson.core</groupId>
						<artifactId>jackson-core</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>io.fabric8</groupId>
				<artifactId>kubernetes-client</artifactId>
				<version>${kubernetes-client.version}</version>
				<type>test-jar</type>
				<scope>test</scope>
				<exclusions>
					<exclusion>
						<groupId>com.fasterxml.jackson.core</groupId>
						<artifactId>jackson-databind</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.fasterxml.jackson.core</groupId>
						<artifactId>jackson-core</artifactId>
					</exclusion>
				</exclusions>
			</dependency>


		</dependencies>
	</dependencyManagement>
	<profiles>
		<profile>
			<id>spring</id>
			<repositories>
				<repository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/libs-snapshot-local</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</repository>
				<repository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
				<repository>
					<id>spring-releases</id>
					<name>Spring Releases</name>
					<url>https://repo.spring.io/release</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
			</repositories>
			<pluginRepositories>
				<pluginRepository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/libs-snapshot-local</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</pluginRepository>
				<pluginRepository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</pluginRepository>
			</pluginRepositories>
		</profile>
	</profiles>
</project>
