<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2013-2019 the original author or authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.cloud</groupId>
		<artifactId>spring-cloud-dependencies-parent</artifactId>
		<version>2.1.5.RELEASE</version>
		<relativePath/>
	</parent>
	<artifactId>spring-cloud-aws-dependencies</artifactId>
	<version>2.1.2.RELEASE</version>
	<packaging>pom</packaging>
	<name>Spring Cloud AWS Dependencies</name>
	<description>Spring Cloud AWS Dependencies</description>
	<properties>
		<aws-java-sdk.version>1.11.415</aws-java-sdk.version>
		<elasticache.version>1.1.1</elasticache.version>
		<jmemcached.version>1.0.0</jmemcached.version>
		<spring-cloud-context.version>1.3.2.RELEASE</spring-cloud-context.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.amazonaws</groupId>
				<artifactId>aws-java-sdk-bom</artifactId>
				<version>${aws-java-sdk.version}</version>
				<type>pom</type>
				<scope>import</scope>
				<exclusions>
					<exclusion>
						<groupId>com.github.tomakehurst</groupId>
						<artifactId>wiremock</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.amazonaws</groupId>
				<artifactId>aws-java-sdk-core</artifactId>
				<version>${aws-java-sdk.version}</version>
				<exclusions>
					<exclusion>
						<groupId>stax</groupId>
						<artifactId>stax-api</artifactId>
					</exclusion>
					<exclusion>
						<groupId>activation</groupId>
						<artifactId>activation</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>commons-logging</artifactId>
						<groupId>commons-logging</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.thimbleware.jmemcached</groupId>
				<artifactId>jmemcached-core</artifactId>
				<version>${jmemcached.version}</version>
			</dependency>
			<dependency>
				<groupId>com.amazonaws</groupId>
				<artifactId>elasticache-java-cluster-client</artifactId>
				<version>${elasticache.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-aws</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-aws-jdbc</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-aws-messaging</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-aws-parameter-store-config</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-aws-secrets-manager-config</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-aws-context</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-aws-messaging</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-aws-jdbc</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-aws-core</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-aws-autoconfigure</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-aws-actuator</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-aws-parameter-store-config</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-aws-secrets-manager-config</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-context</artifactId>
				<version>${spring-cloud-context.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<profiles>
		<profile>
			<id>spring</id>
			<repositories>
				<repository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/libs-snapshot-local</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</repository>
				<repository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
				<repository>
					<id>spring-releases</id>
					<name>Spring Releases</name>
					<url>https://repo.spring.io/release</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
			</repositories>
			<pluginRepositories>
				<pluginRepository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/libs-snapshot-local</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</pluginRepository>
				<pluginRepository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</pluginRepository>
			</pluginRepositories>
		</profile>
	</profiles>
</project>
