<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.kohsuke</groupId>
  <artifactId>pom</artifactId>
  <version>10</version>
  <packaging>pom</packaging>
  <name>Root POM for <PERSON><PERSON><PERSON>'s projects</name>
  <url>http://kohsuke.org/</url>
  <description>${project.name}</description>

  <scm>
    <connection>scm:git:**************/kohsuke/${project.artifactId}.git</connection>
    <developerConnection>scm:git:ssh://**************/kohsuke/${project.artifactId}.git</developerConnection>
    <url>http://${project.artifactId}.kohsuke.org/</url>
    <tag>pom-10</tag>
  </scm>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <name>Nexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <developers>
    <developer>
      <name>Kohsuke Kawaguchi</name>
      <id>kohsuke</id>
      <email><EMAIL></email>
    </developer>
  </developers>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.5</source>
          <target>1.5</target>
         </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <releaseProfiles>release</releaseProfiles>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.3</version>
        <dependencies>
          <dependency>
            <groupId>org.kohsuke</groupId>
            <artifactId>doxia-module-markdown</artifactId>
            <version>1.0</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.2</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>sonatype-nexus-staging</serverId>
          <nexusUrl>https://oss.sonatype.org/</nexusUrl>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.gmaven</groupId>
        <artifactId>gmaven-plugin</artifactId>
        <version>1.4</version>
        <executions>
          <execution>
            <goals>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <providerSelection>2.0</providerSelection>
        </configuration>
      </plugin>
    </plugins>

    <extensions>
      <extension>
        <groupId>org.apache.maven.scm</groupId>
        <artifactId>maven-scm-provider-gitexe</artifactId>
        <version>1.3</version>
      </extension>
      <extension>
        <groupId>org.apache.maven.scm</groupId>
        <artifactId>maven-scm-manager-plexus</artifactId>
        <version>1.3</version>
      </extension>
      <extension>
        <groupId>org.kohsuke</groupId>
        <artifactId>wagon-gitsite</artifactId>
        <version>0.3.5</version>
      </extension>
    </extensions>
  </build>
    
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>
  
  <licenses>
    <license>
        <name>MIT license</name>
        <url>http://www.opensource.org/licenses/mit-license.php</url>
        <distribution>repo</distribution>
    </license>
  </licenses>

  <profiles> 
    <profile> 
      <id>release</id> 
      <build> 
        <plugins> 
          <plugin> 
            <artifactId>maven-gpg-plugin</artifactId> 
            <executions> 
              <execution> 
                <id>sign-artifacts</id> 
                <phase>verify</phase> 
                <goals> 
                  <goal>sign</goal> 
                </goals> 
              </execution> 
            </executions> 
          </plugin> 
        </plugins> 
      </build> 
    </profile> 
  </profiles> 
</project>
