<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.github.houbb</groupId>
    <artifactId>heaven</artifactId>
    <version>0.1.76</version>

    <properties>
        <!--============================== All Plugins START ==============================-->
        <plugin.compiler.version>3.2</plugin.compiler.version>
        <plugin.compiler.version>3.2</plugin.compiler.version>
        <plugin.surefire.version>2.18.1</plugin.surefire.version>
        <plugin.surefire.skip-it>false</plugin.surefire.skip-it>
        <plugin.surefire.ignore-failure>false</plugin.surefire.ignore-failure>

        <plugin.maven-source-plugin.version>2.2.1</plugin.maven-source-plugin.version>
        <plugin.maven-javadoc-plugin.version>2.9.1</plugin.maven-javadoc-plugin.version>
        <plugin.maven-gpg-plugin.version>1.5</plugin.maven-gpg-plugin.version>

        <plugin.coveralls.version>4.3.0</plugin.coveralls.version>
        <plugin.cobertura.version>2.7</plugin.cobertura.version>

        <!--============================== MAIN ==============================-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.compiler.level>1.7</project.compiler.level>

        <!--============================== INTER ==============================-->
        <plugin.gen.version>1.0.6</plugin.gen.version>

        <!--============================== OTHER ==============================-->
        <junit.version>4.12</junit.version>
        <apiguardian-api.version>1.0.0</apiguardian-api.version>
    </properties>



    <dependencyManagement>

        <dependencies>
            <!--============================== OTHER ==============================-->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.apiguardian</groupId>
                <artifactId>apiguardian-api</artifactId>
                <version>${apiguardian-api.version}</version>
                <optional>true</optional>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <dependencies>
        <!--============================== OTHER ==============================-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apiguardian</groupId>
            <artifactId>apiguardian-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!--compiler plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${plugin.compiler.version}</version>
                <configuration>
                    <source>${project.compiler.level}</source>
                    <target>${project.compiler.level}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <compilerArgument>-proc:none</compilerArgument>
                </configuration>
            </plugin>

            <!-- Javadoc -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${plugin.maven-javadoc-plugin.version}</version>
            </plugin>

            <plugin>
                <groupId>com.github.houbb</groupId>
                <artifactId>gen-maven-plugin</artifactId>
                <version>${plugin.gen.version}</version>
                <configuration>
                    <junitVersion>4</junitVersion>
                    <isOverwriteWhenExists>false</isOverwriteWhenExists>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!--============================== ADD For sonatype START ==============================-->
    <name>heaven</name>
    <description>Collect base useful tools for dev.</description>

    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>7</version>
    </parent>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <url>https://github.com/houbb/heaven</url>
        <connection>https://github.com/houbb/heaven.git</connection>
        <developerConnection>https://houbb.github.io/</developerConnection>
    </scm>
    <developers>
        <developer>
            <name>houbb</name>
            <email><EMAIL></email>
            <url>https://houbb.github.io/</url>
        </developer>
    </developers>
    <!--============================== ADD For sonatype END ==============================-->


    <!--mvn clean deploy -P release -Darguments="gpg.passphrase=设置gpg设置密钥时候输入的Passphrase"-->
    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <!-- Source -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${plugin.maven-source-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Javadoc -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${plugin.maven-javadoc-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- GPG -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${plugin.maven-gpg-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <!--=================================== coveralls START ===================================-->
                    <!--mvn cobertura:cobertura coveralls:report -DrepoToken=yourcoverallsprojectrepositorytoken-->
                    <plugin>
                        <groupId>org.eluder.coveralls</groupId>
                        <artifactId>coveralls-maven-plugin</artifactId>
                        <version>${plugin.coveralls.version}</version>
                    </plugin>

                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>cobertura-maven-plugin</artifactId>
                        <version>${plugin.cobertura.version}</version>
                        <configuration>
                            <format>xml</format>
                            <maxmem>256m</maxmem>
                            <!-- aggregated reports for multi-module projects -->
                            <aggregate>true</aggregate>
                            <instrumentation>
                                <excludes>
                                    <exclude>**/*Test.class</exclude>
                                </excludes>
                            </instrumentation>
                        </configuration>
                    </plugin>
                    <!--=================================== coveralls END ===================================-->

                </plugins>
            </build>
            <distributionManagement>
                <snapshotRepository>
                    <id>oss</id>
                    <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
                </snapshotRepository>
                <repository>
                    <id>oss</id>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>


</project>
