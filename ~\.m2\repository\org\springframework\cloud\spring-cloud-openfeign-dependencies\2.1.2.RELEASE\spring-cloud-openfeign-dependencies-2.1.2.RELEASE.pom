<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>spring-cloud-dependencies-parent</artifactId>
		<groupId>org.springframework.cloud</groupId>
		<version>2.1.5.RELEASE</version>
		<relativePath/>
	</parent>
	<artifactId>spring-cloud-openfeign-dependencies</artifactId>
	<version>2.1.2.RELEASE</version>
	<packaging>pom</packaging>
	<name>spring-cloud-openfeign-dependencies</name>
	<description>Spring Cloud OpenFeign Dependencies</description>
	<properties>
		<feign.version>10.2.3</feign.version>
		<feign-form.version>3.8.0</feign-form.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-openfeign-core</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-openfeign</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-core</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign.form</groupId>
				<artifactId>feign-form-spring</artifactId>
				<version>${feign-form.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-slf4j</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-httpclient</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-hystrix</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-java8</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-okhttp</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-gson</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-jackson-jaxb</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-jackson</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-jaxb</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-jaxrs</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-ribbon</artifactId>
				<version>${feign.version}</version>
			</dependency>
			<dependency>
				<groupId>io.github.openfeign</groupId>
				<artifactId>feign-sax</artifactId>
				<version>${feign.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<profiles>
		<profile>
			<id>spring</id>
			<repositories>
				<repository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/libs-snapshot-local</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</repository>
				<repository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
				<repository>
					<id>spring-releases</id>
					<name>Spring Releases</name>
					<url>https://repo.spring.io/release</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
			</repositories>
			<pluginRepositories>
				<pluginRepository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/libs-snapshot-local</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</pluginRepository>
				<pluginRepository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</pluginRepository>
			</pluginRepositories>
		</profile>
	</profiles>
</project>
