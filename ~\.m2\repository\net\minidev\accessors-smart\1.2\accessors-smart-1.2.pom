<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- this POM is released under an Apache 2.0 license -->
    <modelVersion>4.0.0</modelVersion>
    <groupId>net.minidev</groupId>
    <artifactId>accessors-smart</artifactId>
    <version>1.2</version>
    <name>ASM based accessors helper used by json-smart</name>
    <description>Java reflect give poor performance on getter setter an constructor calls, accessors-smart use ASM to speed up those calls.
    </description>
    <packaging>bundle</packaging>
    <url>http://www.minidev.net/</url>
    <organization>
        <name>Chemouni Uriel</name>
        <url>http://www.minidev.net/</url>
    </organization>
    <developers>
        <developer>
            <id>uriel</id>
            <name><PERSON><PERSON></name>
            <email><EMAIL></email>
            <timezone>GMT-7</timezone>
            <roles>
            </roles>
        </developer>
    </developers>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
            <comments>All files under Apache 2</comments>
        </license>
    </licenses>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.5</maven.compiler.source>
        <maven.compiler.target>1.5</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>5.0.4</version>
        </dependency>
    </dependencies>

    <!-- updated on 29/10/2015 -->
                <!--
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>2.17</version>
                <configuration>
                    <configLocation>google_checks.xml</configLocation>
                </configuration>
            </plugin>
        </plugins>
    </reporting>
     -->
    <scm>
        <!-- ON GIT HUB -->
        <connection>scm:git:https://github.com/netplex/json-smart-v2.git</connection>
        <developerConnection>scm:git:https://github.com/netplex/json-smart-v2.git</developerConnection>
        <url>https://github.com/netplex/json-smart-v2</url>
    </scm>
    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>
    <profiles>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <!-- will be set by the release plugin upon performing mvn release:perform -->
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
				<!-- My old Is back -->
				<gpg.keyname>2C8DF6EC</gpg.keyname>
                <!-- <gpg.keyname>8E322ED0</gpg.keyname> -->
                <!-- <gpg.keyname>Uriel Chemouni (dev) <<EMAIL>></gpg.keyname> -->
                <!-- GPG Key ID to use for signing -->
            </properties>
            <build>
                <plugins>
                    <!-- Enable signing of the artifacts For gpg:sign-and-deploy-file it's 
						necessary to have a <server> with the repositoryId provided or id="remote-repository" 
						defined in settings.xml (it contains the repository's login, psw) Signing: 
						mvn gpg:sign-and-deploy-file -DpomFile=target/myapp-1.0.pom -Dfile=target/myapp-1.0.jar 
						-Durl=http://oss.sonatype.org/content/repositories/malyvelky/ -DrepositoryId=sonatype_oss 
						Note normally it uses the defaul key but we can ovveride it by either setting 
						the property gpg.keyname (done in this POM) or by providing -Dkeyname=66AE163A 
						on the command line. OR directly w/ gpg (remove space in - -): gpg -u 66AE163A 
						- -sign - -detach-sign -a target/dbunit-embeddedderby-parenttest.jar Note: 
						"mvn gpg:sign" results in NPE with v 1.o-a.-4, use "mvn package gpg:sign" 
						instead; see the issue MGPG-18 -->
                    <plugin>
                        <!-- updated on 29/07/2015 -->
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Publish also javadocs when releasing - required by Sonatype -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Release Plugin (Update version in POM before/after release, create 
						tag, deploy) to try: mvn release:prepare -DdryRun=true && mvn release:clean 
						to perform: mvn release:prepare release:perform Read http://nexus.sonatype.org/oss-repository-hosting.html#3 
						for instructions on releasing to this project's Sonatype repository -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-release-plugin</artifactId>
                        <version>2.5.2</version>
                        <configuration>
                            <mavenExecutorId>forked-path</mavenExecutorId>
                            <arguments>-Psonatype-oss-release</arguments>
                            <autoVersionSubmodules>false</autoVersionSubmodules>
                            <useReleaseProfile>false</useReleaseProfile>
                            <releaseProfiles>release</releaseProfiles>
                            <goals>deploy</goals>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>include-sources</id>
            <build>
                <resources>
                    <resource>
                        <targetPath>/</targetPath>
                        <filtering>true</filtering>
                        <directory>src/main/java</directory>
                        <includes>
                            <include>**/*.java</include>
                        </includes>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>
    <build>
        <plugins>
            <plugin>
                <!-- updated on 29/07/2015 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>bind-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <!-- updated on 29/10/2015 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>1.6</source>
                    <target>1.6</target>
                    <excludes>
                        <exclude>**/.svn/*</exclude>
                        <exclude>**/.svn</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <!-- updated on 29/10/2015 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <!-- updated on 29/10/2015 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <excludes>
                        <exclude>**/.svn/*</exclude>
                        <exclude>**/.svn</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <!-- updated on 29/07/2015 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.3</version>
                <!-- ONLY NEEDED With jdk 1.7+ -->
                <configuration>
                    <failOnError>false</failOnError>
                    <!-- <additionalparam>-Xdoclint:none</additionalparam> -->
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>3.3.0</version> <!-- 3.0.0 need java 7+ -->
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Bundle-SymbolicName>${project.groupId}.${project.artifactId}</Bundle-SymbolicName>
                        <Bundle-Name>${project.artifactId}</Bundle-Name>
                        <Bundle-Version>${project.version}</Bundle-Version>
                        <Export-Package>
                            net.minidev.asm, net.minidev.asm.ex
                        </Export-Package>
                    </instructions>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
