<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy</groupId>
    <artifactId>pay-common</artifactId>
    <version>20250401-RELEASE</version>
    <packaging>pom</packaging>
    <name>pay-common</name>
    <description>支付公共工程</description>
    <issueManagement>
        <system>JIRA</system>
        <url>http://ims.intelnal.howbuy.com/</url>
    </issueManagement>
    <repositories>
        <repository>
            <id>ReleaseRepo</id>
            <url>http://nexus.howbuy.pa/repository/public/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>public-plugin</id>
            <url>http://nexus.howbuy.pa/repository/public/</url>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <id>howbuy-releases</id>
            <name>howbuy-releases</name>
            <url>http://nexus.howbuy.pa/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshots</id>
            <name>howbuy-snapshots</name>
            <url>http://nexus.howbuy.pa/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <properties>
        
        <java.version>1.7</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <junit.version>4.12</junit.version>
        <lombok.version>1.16.4</lombok.version>
        <com.howbuy.common.version>1.0.0-SNAPSHOT</com.howbuy.common.version>
        <org.slf4j.version>1.7.4</org.slf4j.version>
        <fastjson.version>1.1.41</fastjson.version>
        <org.springframework.security.version>3.2.6.RELEASE</org.springframework.security.version>
        <org.jodd.version>3.5.2</org.jodd.version>
        <zookeeper.version>3.4.6</zookeeper.version>
        <commons.lang.version>2.4</commons.lang.version>
        <cache.version>3.0.1-RELEASE</cache.version>
        
        <javax.servlet.version>3.1.0</javax.servlet.version>
        <org.apache.poi.version>5.2.2</org.apache.poi.version>
        <dom4j.version>1.6.1</dom4j.version>
        
        
        <org.freemarker.version>2.3.16</org.freemarker.version>
        <com.itextpdf.version>5.5.9</com.itextpdf.version>
        
        <com.howbuy.pay-common.version>20250401-RELEASE</com.howbuy.pay-common.version>
        <com.howbuy.common-service.version>3.5.7-RELEASE</com.howbuy.common-service.version>
        <com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.howbuy-dfile-service.version>1.18.1-RELEASE</com.howbuy.howbuy-dfile-service.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.howbuy.common</groupId>
                <artifactId>common-service</artifactId>
                <version>${com.howbuy.common-service.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${org.slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sun.jmx</groupId>
                        <artifactId>jmxri</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.jdmk</groupId>
                        <artifactId>jmxtools</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.jms</groupId>
                        <artifactId>jms</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>pay-common-model</artifactId>
                <version>${com.howbuy.pay-common.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${org.springframework.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jms</artifactId>
                <version>${org.springframework.security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jodd</groupId>
                <artifactId>jodd-vtor</artifactId>
                <version>${org.jodd.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons.lang.version}</version>
            </dependency>
            
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.servlet.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${org.apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>${com.itextpdf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-pdfa</artifactId>
                <version>${com.itextpdf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-xtra</artifactId>
                <version>${com.itextpdf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf.tool</groupId>
                <artifactId>xmlworker</artifactId>
                <version>${com.itextpdf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${org.freemarker.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-service</artifactId>
                <version>2.2.1-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-amq</artifactId>
                <version>2.2.1-RELEASE</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-cachemanagement</artifactId>
                <version>${com.howbuy.howbuy-cachemanagement.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>activemq-all</artifactId>
                        <groupId>org.apache.activemq</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-ccms-watcher</artifactId>
                <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-service</artifactId>
                <version>${com.howbuy.howbuy-dfile-service.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <defaultGoal>install</defaultGoal>
        <pluginManagement>
            <plugins>
                
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.2</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <debug>true</debug>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>