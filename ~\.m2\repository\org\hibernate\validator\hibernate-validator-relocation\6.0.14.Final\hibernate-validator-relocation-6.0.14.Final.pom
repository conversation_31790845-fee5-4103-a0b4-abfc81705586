<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Hibernate Validator, declare and validate application constraints
  ~
  ~ License: Apache License, Version 2.0
  ~ See the license.txt file in the root directory or <http://www.apache.org/licenses/LICENSE-2.0>.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator-parent</artifactId>
        <version>6.0.14.Final</version>
    </parent>

    <artifactId>hibernate-validator-relocation</artifactId>
    <packaging>pom</packaging>

    <name>Hibernate Validator Relocation Artifacts</name>

    <modules>
        <module>annotation-processor</module>
        <module>cdi</module>
        <module>engine</module>
        <module>karaf-features</module>
    </modules>
</project>
