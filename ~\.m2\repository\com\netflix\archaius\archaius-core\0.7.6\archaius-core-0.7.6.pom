<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.netflix.archaius</groupId>
  <artifactId>archaius-core</artifactId>
  <version>0.7.6</version>
  <dependencies>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>commons-configuration</groupId>
      <artifactId>commons-configuration</artifactId>
      <version>1.8</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.6.4</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>16.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.4.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.4.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.4.3</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
  <name>archaius-core</name>
  <description>archaius-core</description>
  <developers>
    <developer>
      <id>netflixgithub</id>
      <name>Netflix Open Source Development</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <properties>
    <nebula_Manifest_Version>1.0</nebula_Manifest_Version>
    <nebula_Implementation_Title>com.netflix.archaius#archaius-core;0.7.6</nebula_Implementation_Title>
    <nebula_Implementation_Version>0.7.6</nebula_Implementation_Version>
    <nebula_Built_Status>integration</nebula_Built_Status>
    <nebula_Built_By>travis</nebula_Built_By>
    <nebula_Built_OS>Linux</nebula_Built_OS>
    <nebula_Build_Date>2017-11-08_20:31:22</nebula_Build_Date>
    <nebula_Gradle_Version>2.2.1</nebula_Gradle_Version>
    <nebula_Module_Owner><EMAIL></nebula_Module_Owner>
    <nebula_Module_Email><EMAIL></nebula_Module_Email>
    <nebula_Module_Source>/archaius-core</nebula_Module_Source>
    <nebula_Module_Origin>https://github.com/Netflix/archaius.git</nebula_Module_Origin>
    <nebula_Change>8fa8499</nebula_Change>
    <nebula_Branch>8fa8499ff43abe1ea315e4e2c3ad50e47c44ea0f</nebula_Branch>
    <nebula_Build_Host>travis-job-netflix-archaius-299293571.travisci.net</nebula_Build_Host>
    <nebula_Build_Job>LOCAL</nebula_Build_Job>
    <nebula_Build_Number>LOCAL</nebula_Build_Number>
    <nebula_Build_Id>LOCAL</nebula_Build_Id>
    <nebula_Created_By>1.8.0_144-b01 (Oracle Corporation)</nebula_Created_By>
    <nebula_Build_Java_Version>1.8.0_144</nebula_Build_Java_Version>
    <nebula_X_Compile_Target_JDK>1.6</nebula_X_Compile_Target_JDK>
    <nebula_X_Compile_Source_JDK>1.6</nebula_X_Compile_Source_JDK>
  </properties>
  <scm>
    <url>scm:https://github.com/Netflix/archaius.git</url>
    <connection>scm:https://github.com/Netflix/archaius.git</connection>
  </scm>
  <url>https://github.com/Netflix/archaius</url>
</project>
