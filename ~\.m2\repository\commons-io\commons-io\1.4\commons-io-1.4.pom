<?xml version="1.0"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>7</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-io</groupId>
  <artifactId>commons-io</artifactId>
  <version>1.4</version>
  <name>Commons IO</name>

  <inceptionYear>2002</inceptionYear>
  <description>
        Commons-IO contains utility classes, stream implementations, file filters, file comparators and endian classes.
  </description>

  <url>http://commons.apache.org/io/</url>

  <issueManagement>
    <system>jira</system>
    <url>http://issues.apache.org/jira/browse/IO</url>
  </issueManagement>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/commons/proper/io/trunk</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/commons/proper/io/trunk</developerConnection>
    <url>http://svn.apache.org/viewvc/commons/proper/io/trunk</url>
  </scm>

  <developers>
    <developer>
      <name>Scott Sanders</name>
      <id>sanders</id>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>dIon Gillard</name>
      <id>dion</id>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Nicola Ken Barozzi</name>
      <id>nicolaken</id>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Henri Yandell</name>
      <id>bayard</id>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Stephen Colebourne</name>
      <id>scolebourne</id>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
      <timezone>0</timezone>
    </developer>
    <developer>
      <name>Jeremias Maerki</name>
      <id>jeremias</id>
      <email><EMAIL></email>
      <organization/>
      <roles>
        <role>Java Developer</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <name>Matthew Hawthorne</name>
      <id>matth</id>
      <email><EMAIL></email>
      <organization/>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Martin Cooper</name>
      <id>martinc</id>
      <email><EMAIL></email>
      <organization/>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Rob Oxspring</name>
      <id>roxspring</id>
      <email><EMAIL></email>
      <organization/>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Jochen Wiedmann</name>
      <id>jochen</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Niall Pemberton</name>
      <id>niallp</id>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Jukka Zitting</name>
      <id>jukka</id>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Rahul Akolkar</name>
    </contributor>
    <contributor>
      <name>Jason Anderson</name>
    </contributor>
    <contributor>
      <name>Nathan Beyer</name>
    </contributor>
    <contributor>
      <name>Emmanuel Bourg</name>
    </contributor>
    <contributor>
      <name>Chris Eldredge</name>
    </contributor>
    <contributor>
      <name>Magnus Grimsell</name>
    </contributor>
    <contributor>
      <name>Jim Harrington</name>
    </contributor>
    <contributor>
      <name>Thomas Ledoux</name>
    </contributor>
    <contributor>
      <name>Andy Lehane</name>
    </contributor>
    <contributor>
      <name>Marcelo Liberato</name>
    </contributor>
    <contributor>
      <name>Alban Peignier</name>
      <email>alban.peignier at free.fr</email>
    </contributor>
    <contributor>
      <name>Ian Springer</name>
    </contributor>
    <contributor>
      <name>Masato Tezuka</name>
    </contributor>
    <contributor>
      <name>James Urie</name>
    </contributor>
    <contributor>
      <name>Frank W. Zammetti</name>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <sourceDirectory>src/java</sourceDirectory>
    <testSourceDirectory>src/test</testSourceDirectory>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <includes>
              <include>**/*Test*</include>
            </includes>
            <excludes>
              <exclude>**/*AbstractTestCase*</exclude>
              <exclude>**/AllIOTestSuite*</exclude>
              <exclude>**/PackageTestSuite*</exclude>
              <exclude>**/testtools/**</exclude>

              <!-- http://jira.codehaus.org/browse/SUREFIRE-44 -->
              <exclude>**/*$*</exclude>
            </excludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/main/assembly/bin.xml</descriptor>
            <descriptor>src/main/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>

      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <!--
                 Configure OSGi bundle information
                 (remove when maven bundle plugin is added to commons-parent pom)
             -->
            <manifestEntries>
              <Bundle-SymbolicName>org.apache.commons.io</Bundle-SymbolicName>
              <Bundle-License>http://www.apache.org/licenses/LICENSE-2.0.txt</Bundle-License>
              <Bundle-ManifestVersion>2</Bundle-ManifestVersion>
              <Bundle-Name>Apache Commons IO Bundle</Bundle-Name>
              <Bundle-Vendor>${project.organization.name}</Bundle-Vendor>
              <Bundle-Version>${project.version}</Bundle-Version>
              <Export-Package>
org.apache.commons.io;version=${project.version},
org.apache.commons.io.comparator;version=${project.version},
org.apache.commons.io.filefilter;version=${project.version},
org.apache.commons.io.input;version=${project.version},
org.apache.commons.io.output;version=${project.version}
              </Export-Package>
              <Import-Package>
org.apache.commons.io;version=${project.version},
org.apache.commons.io.comparator;version=${project.version},
org.apache.commons.io.filefilter;version=${project.version},
org.apache.commons.io.input;version=${project.version},
org.apache.commons.io.output;version=${project.version}
              </Import-Package>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changes-plugin</artifactId>
        <version>2.0-beta-3</version>
        <configuration>
          <issueLinkTemplate>%URL%/../%ISSUE%</issueLinkTemplate>
          <!--
             JIRA Search parameters:
                 - resolution=1 specifies "Fixed"
                 - fixfor=12312101 specifies "Commons IO version 1.4"

             If/when http://jira.codehaus.org/browse/MCHANGES-92
             is fixed this will be easier/less messy
           -->
          <filter>&amp;&amp;resolution=1&amp;fixfor=12312101</filter>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>jira-report</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>2.1</version>
        <configuration>
          <configLocation>checkstyle.xml</configLocation>
          <enableRulesSummary>false</enableRulesSummary>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.3</version>
        <configuration>
          <source>1.4</source>
          <links>
            <link>http://java.sun.com/j2se/1.4.2/docs/api</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <version>2.1</version>
        <configuration>
          <comparisonVersion>1.3.2</comparisonVersion>
          <minSeverity>info</minSeverity>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-site-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>site</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>run</goal>
                </goals>
                <phase>package</phase>
                <configuration>
                  <tasks>
                    <copy todir="${project.build.directory}/site/api-release">
                      <fileset dir="${project.build.directory}/site/apidocs"/>
                    </copy>
                  </tasks>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>attached</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>rc</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-site-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>site</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>attached</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
