<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Hibernate Validator, declare and validate application constraints
  ~
  ~ License: Apache License, Version 2.0
  ~ See the license.txt file in the root directory or <http://www.apache.org/licenses/LICENSE-2.0>.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator-parent</artifactId>
        <version>6.0.14.Final</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>hibernate-validator</artifactId>

    <name>Hibernate Validator Engine</name>
    <description>Hibernate's Bean Validation (JSR-380) reference implementation.</description>

    <properties>
        <hibernate-validator-parent.path>..</hibernate-validator-parent.path>
    </properties>

    <distributionManagement>
        <site>
            <id>site</id>
            <url>http://hibernate.org/validator</url>
        </site>
    </distributionManagement>

    <dependencies>
        <!--
        Compile time dependencies
        -->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml</groupId>
            <artifactId>classmate</artifactId>
        </dependency>

        <!--
        Provided dependencies
        -->
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>javax.el</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging-processor</artifactId>
            <!--
            HV-963
            This is actually not a dependency which is needed at runtime, however,
            Maven does not have a compile time only scope. The dependency is needed to
            run the JBoss Logging annotation processor as part of the main compilation.
            Trying different setups via compiler plugin local dependencies or extensions
            all fail. See also http://stackoverflow.com/questions/14322904/maven-3-how-to-add-annotation-processor-dependency
            -->
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging-annotations</artifactId>
            <scope>provided</scope>
        </dependency>

        <!--
        Optional dependencies
        -->
        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>javax.persistence-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.paranamer</groupId>
            <artifactId>paranamer</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>javax.money</groupId>
            <artifactId>money-api</artifactId>
            <optional>true</optional>
        </dependency>

        <!--
        Test dependencies
        -->
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>hibernate-validator-test-utils</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-jsr223</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.shrinkwrap</groupId>
            <artifactId>shrinkwrap-impl-base</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.javamoney</groupId>
            <artifactId>moneta</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <defaultGoal>test</defaultGoal>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/xsd</directory>
                <targetPath>META-INF</targetPath>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <filtering>true</filtering>
                <directory>src/test/resources</directory>
                <includes>
                    <include>META-INF/services/*</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>de.thetaphi</groupId>
                <artifactId>forbiddenapis</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-jar</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <archive>
                                <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                                <manifestEntries>
                                    <Specification-Title>Bean Validation</Specification-Title>
                                    <Specification-Version>2.0</Specification-Version>
                                    <Automatic-Module-Name>${hibernate-validator.module-name}</Automatic-Module-Name>
                                </manifestEntries>
                            </archive>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <instructions>
                        <Bundle-SymbolicName>${hibernate-validator.bundle-name}</Bundle-SymbolicName>
                        <Import-Package>
                            javax.persistence.*;version="[2.0.0,3.0.0)";resolution:=optional,
                            javax.validation.*;version="[2.0.0,3.0.0)",
                            javax.script.*;version="0",
                            javax.xml.*;version="0",
                            javax.el.*;version="[2.0.0,4.0.0)";resolution:=optional,
                            org.xml.sax.*;version="0",
                            org.jboss.logging.*;version="[3.1.0,4.0.0)",
                            com.fasterxml.classmate.*;version="[1.3,2.0.0)",
                            org.joda.time.*;version="[2.0.0,3.0.0)";resolution:=optional,
                            org.jsoup.*;version="[1.5.2,2.0.0)";resolution:=optional,
                            javax.money;version="[1.0.0,2.0.0)";resolution:=optional,
                            com.thoughtworks.paranamer.*;version="[2.5.5,3.0.0)";resolution:=optional
                        </Import-Package>
                        <Export-Package>
                            org.hibernate.validator;version="${project.version}",
                            org.hibernate.validator.cfg.*;version="${project.version}",
                            org.hibernate.validator.constraints.*;version="${project.version}",
                            org.hibernate.validator.constraintvalidation.*;version="${project.version}",
                            org.hibernate.validator.constraintvalidators.*;version="${project.version}",
                            org.hibernate.validator.engine.*;version="${project.version}",
                            org.hibernate.validator.group;version="${project.version}",
                            org.hibernate.validator.messageinterpolation;version="${project.version}",
                            org.hibernate.validator.parameternameprovider;version="${project.version}",
                            org.hibernate.validator.path;version="${project.version}",
                            org.hibernate.validator.resourceloading;version="${project.version}",
                            org.hibernate.validator.spi.*;version="${project.version}"
                        </Export-Package>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <parallel>tests</parallel>
                    <threadCount>4</threadCount>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.github.siom79.japicmp</groupId>
                <artifactId>japicmp-maven-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>jdk9+</id>
            <activation>
                <jdk>[9,)</jdk>
            </activation>
            <properties>
                <maven-surefire-plugin.argLine>--illegal-access=deny</maven-surefire-plugin.argLine>
            </properties>
        </profile>
        <profile>
            <id>jdk11+</id>
            <activation>
                <jdk>[11,)</jdk>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>org.openjfx</groupId>
                    <artifactId>javafx-base</artifactId>
                    <version>${version.org.openjfx}</version>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>sigtest</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy-tck-bv-api-signature-file</id>
                                <phase>generate-test-sources</phase>
                                <goals>
                                    <goal>unpack</goal>
                                </goals>
                                <configuration>
                                    <artifactItems>
                                        <artifactItem>
                                            <groupId>org.hibernate.beanvalidation.tck</groupId>
                                            <artifactId>beanvalidation-tck-tests</artifactId>
                                            <version>${tck.version}</version>
                                            <type>jar</type>
                                            <overWrite>true</overWrite>
                                        </artifactItem>
                                    </artifactItems>
                                    <!-- We just need the signature file and nothing else -->
                                    <includes>**/*.sig</includes>
                                    <outputDirectory>${project.build.directory}/api-signature</outputDirectory>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.netbeans.tools</groupId>
                        <artifactId>sigtest-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <packages>javax.validation,javax.validation.bootstrap,javax.validation.constraints,
                                javax.validation.constraintvalidation,javax.validation.executable,javax.validation.groups,
                                javax.validation.metadata,javax.validation.spi,javax.validation.valueextraction
                            </packages>
                            <sigfile>${project.build.directory}/api-signature/validation-api-java8.sig</sigfile>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
