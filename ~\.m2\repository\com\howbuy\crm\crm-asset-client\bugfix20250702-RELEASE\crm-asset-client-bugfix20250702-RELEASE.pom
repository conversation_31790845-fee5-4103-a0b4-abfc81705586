<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>crm-asset-server</artifactId>
        <groupId>com.howbuy.crm</groupId>
        <version>bugfix20250702-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>crm-asset-client</name>
    <artifactId>crm-asset-client</artifactId>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

        <dependencies>
            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>base-commons</artifactId>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
            </dependency>

        </dependencies>

</project>