<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.infradna.tool</groupId>
  <artifactId>bridge-method-injector-parent</artifactId>
  <version>1.12</version>
  <packaging>pom</packaging>

  <name>Bridge Method Injection Parent POM</name>
  <description>Evolve your classes without breaking compatibility</description>
  <url>http://bridge-method-injector.infradna.com/</url>

  <modules>
    <module>annotation</module>
    <module>injector</module>
  </modules>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.5</source>
          <target>1.5</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.0</version>
        <configuration>
          <releaseProfiles>release</releaseProfiles>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.apache.maven.scm</groupId>
            <artifactId>maven-scm-provider-gitexe</artifactId>
            <version>1.2</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.4.4</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>sonatype-nexus-staging</serverId>
          <nexusUrl>https://oss.sonatype.org/</nexusUrl>
        </configuration>
      </plugin>
    </plugins>
    <extensions>
      <extension>
        <groupId>org.jvnet.wagon-svn</groupId>
        <artifactId>wagon-svn</artifactId>
        <version>1.9</version>
      </extension>
    </extensions>
  </build>

  <scm>
    <connection>scm:git:**************:infradna/bridge-method-injector.git</connection>
    <url>https://github.com/infradna/bridge-method-injector</url>
  </scm>

  <developers>
    <developer>
      <id>kohsuke</id>
      <name>Kohsuke Kawaguchi</name>
    </developer>
  </developers>

  <licenses>
    <license>
      <name>MIT License</name>
      <distribution>repository</distribution>
      <url>http://www.opensource.org/licenses/mit-license.php</url>
    </license>
  </licenses>

  <distributionManagement>
    <repository>
      <id>sonatype-nexus-staging</id>
      <name>Nexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>


  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <aggregate>true</aggregate>
        </configuration>
      </plugin>
    </plugins>
  </reporting>
  
  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
