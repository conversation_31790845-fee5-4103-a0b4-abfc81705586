<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.netflix.hystrix</groupId>
  <artifactId>hystrix-core</artifactId>
  <version>1.5.18</version>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.netflix.archaius</groupId>
      <artifactId>archaius-core</artifactId>
      <version>0.4.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.reactivex</groupId>
      <artifactId>rxjava</artifactId>
      <version>1.2.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.hdrhistogram</groupId>
      <artifactId>HdrHistogram</artifactId>
      <version>2.1.9</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <name>hystrix-core</name>
  <description>hystrix-core</description>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>netflixgithub</id>
      <name>Netflix Open Source Development</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <properties>
    <nebula_Manifest_Version>1.0</nebula_Manifest_Version>
    <nebula_Implementation_Title>com.netflix.hystrix#hystrix-core;1.5.18</nebula_Implementation_Title>
    <nebula_Implementation_Version>1.5.18</nebula_Implementation_Version>
    <nebula_Built_Status>integration</nebula_Built_Status>
    <nebula_Built_By>travis</nebula_Built_By>
    <nebula_Built_OS>Linux</nebula_Built_OS>
    <nebula_Build_Date>2018-11-16_22:51:19</nebula_Build_Date>
    <nebula_Gradle_Version>3.1</nebula_Gradle_Version>
    <nebula_Module_Owner><EMAIL></nebula_Module_Owner>
    <nebula_Module_Email><EMAIL></nebula_Module_Email>
    <nebula_Module_Source>/hystrix-core</nebula_Module_Source>
    <nebula_Module_Origin>https://github.com/Netflix/Hystrix.git</nebula_Module_Origin>
    <nebula_Change>809104c</nebula_Change>
    <nebula_Branch>809104c8751be0065a8269df3677965f3092e6b9</nebula_Branch>
    <nebula_Build_Host>travis-job-netflix-hystrix-456195345.travisci.net</nebula_Build_Host>
    <nebula_Build_Job>Netflix/Hystrix</nebula_Build_Job>
    <nebula_Build_Number>1500</nebula_Build_Number>
    <nebula_Build_Id>456195344</nebula_Build_Id>
    <nebula_Created_By>1.8.0_151-b12 (Oracle Corporation)</nebula_Created_By>
    <nebula_Build_Java_Version>1.8.0_151</nebula_Build_Java_Version>
    <nebula_X_Compile_Target_JDK>1.6</nebula_X_Compile_Target_JDK>
    <nebula_X_Compile_Source_JDK>1.6</nebula_X_Compile_Source_JDK>
  </properties>
  <url>https://github.com/Netflix/Hystrix</url>
  <scm>
    <url>https://github.com/Netflix/Hystrix.git</url>
  </scm>
</project>
