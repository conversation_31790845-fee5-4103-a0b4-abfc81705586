<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>org.json</groupId>
	<artifactId>json</artifactId>
	<version>20170516</version>
	<packaging>bundle</packaging>

	<name>JSON in Java</name>
	<description>
		JSON is a light-weight, language independent, data interchange format.
		See http://www.JSON.org/

		The files in this package implement JSON encoders/decoders in Java.
		It also includes the capability to convert between JSON and XML, HTTP
		headers, Cookies, and CDL.

		This is a reference implementation. There is a large number of JSON packages
		in Java. Perhaps someday the Java community will standardize on one. Until
		then, choose carefully.

		The license includes this restriction: "The software shall be used for good,
		not evil." If your conscience cannot live with that, then choose a different
		package.
	</description>
	<url>https://github.com/douglascrockford/JSON-java</url>

	<parent>
		<groupId>org.sonatype.oss</groupId>
		<artifactId>oss-parent</artifactId>
		<version>9</version>
	</parent>

	<scm>
		<url>https://github.com/douglascrockford/JSON-java.git</url>
		<connection>scm:git:git://github.com/douglascrockford/JSON-java.git</connection>
		<developerConnection>scm:git:**************:douglascrockford/JSON-java.git</developerConnection>
	</scm>

	<licenses>
		<license>
			<name>The JSON License</name>
			<url>http://json.org/license.html</url>
			<distribution>repo</distribution>
			<comments>Copyright (c) 2002 JSON.org

				Permission is hereby granted, free of charge, to any person obtaining a copy of this software and
				associated documentation files (the "Software"), to deal in the Software without restriction, including
				without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
				copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the
				following conditions:

				The above copyright notice and this permission notice shall be included in all copies or substantial
				portions of the Software.

				The Software shall be used for Good, not Evil.

				THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT
				LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
				NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
				WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
				SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
			</comments>
		</license>
	</licenses>

	<developers>
		<developer>
			<name>Douglas Crockford</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>


	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.1.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>1.9.5</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<version>3.0.1</version>
				<extensions>true</extensions>
				<configuration>
					<instructions>
						<Export-Package>
							org.json
						</Export-Package>
						<Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
					</instructions>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>1.6</source>
					<target>1.6</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.1.2</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.7</version>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<additionalparam>-Xdoclint:none</additionalparam>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-gpg-plugin</artifactId>
				<version>1.5</version>
				<executions>
					<execution>
						<id>sign-artifacts</id>
						<phase>verify</phase>
						<goals>
							<goal>sign</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.sonatype.plugins</groupId>
				<artifactId>nexus-staging-maven-plugin</artifactId>
				<version>1.6.3</version>
				<extensions>true</extensions>
				<configuration>
					<serverId>ossrh</serverId>
					<nexusUrl>https://oss.sonatype.org/</nexusUrl>
					<autoReleaseAfterClose>false</autoReleaseAfterClose>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
