<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>spring-cloud-dependencies-parent</artifactId>
		<groupId>org.springframework.cloud</groupId>
		<version>2.1.5.RELEASE</version>
		<relativePath/>
	</parent>
	<artifactId>spring-cloud-consul-dependencies</artifactId>
	<version>2.1.2.RELEASE</version>
	<packaging>pom</packaging>
	<name>spring-cloud-consul-dependencies</name>
	<description>Spring Cloud Consul Dependencies</description>
	<properties>
		<consul-api.version>1.4.1</consul-api.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-consul-core</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-consul-binder</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-consul-config</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-consul-discovery</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-consul</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-consul-bus</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-consul-config</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-consul-discovery</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-consul-all</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.ecwid.consul</groupId>
				<artifactId>consul-api</artifactId>
				<version>${consul-api.version}</version>
				<exclusions>
					<exclusion>
						<groupId>javax.servlet</groupId>
						<artifactId>servlet-api</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<profiles>
		<profile>
			<id>spring</id>
			<repositories>
				<repository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/libs-snapshot-local</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</repository>
				<repository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
				<repository>
					<id>spring-releases</id>
					<name>Spring Releases</name>
					<url>https://repo.spring.io/release</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
			</repositories>
			<pluginRepositories>
				<pluginRepository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/libs-snapshot-local</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</pluginRepository>
				<pluginRepository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</pluginRepository>
			</pluginRepositories>
		</profile>
	</profiles>
</project>
