<?xml version="1.0"?>
<!--
	<AUTHOR>
	<AUTHOR>
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>net.sf.oval</groupId>
	<artifactId>oval</artifactId>
	<version>1.84</version>
	<packaging>jar</packaging>
	<name>OVal</name>

	<url>http://oval.sf.net</url>

	<description><![CDATA[OVal is a pragmatic and extensible validation framework for any kind of Java objects (not only JavaBeans).

Constraints can be declared with annotations (@NotNull, @MaxLength), POJOs or XML.

Custom constraints can be expressed as custom Java classes or by using scripting languages such as JavaScript, Groovy, BeanShell, OGNL or MVEL.

Besides field/property validation OVal implements Programming by Contract features by utilizing AspectJ based aspects. This for example allows runtime validation of method arguments.]]></description>
	<organization>
		<name>The OVal Development Team</name>
		<url>http://oval.sf.net/</url>
	</organization>

	<licenses>
		<license>
			<name>Eclipse Public License - v 1.0</name>
			<distribution>repo</distribution>
			<!-- <url>http://www.eclipse.org/legal/epl-v10.html</url> -->
			<url>http://oval.sf.net/license/epl-v10.txt</url>
		</license>
	</licenses>

	<developers>
		<developer>
			<id>sebthom</id>
			<name>Sebastian Thomschke</name>
			<email>sebthom at users.sourceforge.net</email>
			<roles>
				<role>lead</role>
				<role>developer</role>
			</roles>
			<timezone>-1</timezone>
		</developer>
		<developer>
			<id>hriegel</id>
			<name>Holger Riegel</name>
			<email>hriegel at users.sourceforge.net</email>
			<roles>
				<role>developer</role>
			</roles>
			<timezone>-1</timezone>
		</developer>
	</developers>

	<contributors>
		<contributor>
			<name>Mirko Humbert</name>
			<url>http://www.mirkohumbert.ch/</url>
			<roles>
				<role>Designer of the OVal logo</role>
			</roles>
		</contributor>
	</contributors>

	<scm>
		<url>https://svn.code.sf.net/p/oval/code/trunk/</url>
		<connection>scm:javasvn:https://svn.code.sf.net/p/oval/code/trunk/</connection>
		<developerConnection>scm:javasvn:https://svn.code.sf.net/p/oval/code/trunk/</developerConnection>
	</scm>

	<ciManagement>
		<system>jenkins</system>
		<url>https://ci.vegardit.com/job/net.sf.oval</url>
	</ciManagement>

	<mailingLists>
		<mailingList>
			<name>Help</name>
			<archive>http://sourceforge.net/forum/forum.php?forum_id=488110</archive>
		</mailingList>
		<mailingList>
			<name>Open Discussion</name>
			<archive>http://sourceforge.net/forum/forum.php?forum_id=488109</archive>
		</mailingList>
	</mailingLists>

	<repositories>
		<repository>
			<id>javax.script-backport</id>
			<!-- <url>http://dist.codehaus.org/mule/dependencies/maven2/</url> -->
			<!-- <url>http://repo1.maven.org/maven/mule/dependencies/maven2/</url> -->
			<url>http://mirrors.ibiblio.org/pub/mirrors/maven/mule/dependencies/maven2/</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>net.sf.oval-repo</id>
			<name>OVal Repository for Maven</name>
			<url>http://oval.sourceforge.net/mvn-repo/release</url>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>Codehaus repository</id>
			<url>http://repository.codehaus.org/</url>
		</pluginRepository>
	</pluginRepositories>

	<distributionManagement>
		<downloadUrl>http://sourceforge.net/project/showfiles.php?group_id=145963</downloadUrl>
		<repository>
			<id>oval-release</id>
			<url>sftp://web.sourceforge.net/home/<USER>/o/ov/oval/htdocs/mvn-repo/release</url>
			<!-- <url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url> -->
		</repository>
		<snapshotRepository>
			<id>oval-snapshot</id>
			<url>sftp://web.sourceforge.net/home/<USER>/o/ov/oval/htdocs/mvn-repo/snapshot</url>
			<!-- <url>https://oss.sonatype.org/content/repositories/snapshots</url> -->
		</snapshotRepository>
		<site>
			<id>oval-site</id>
			<url>sftp://web.sourceforge.net/home/<USER>/o/ov/oval/htdocs/site/</url>
		</site>
	</distributionManagement>

	<inceptionYear>2005</inceptionYear>

	<issueManagement>
		<system>sourceforge</system>
		<url>http://sourceforge.net/tracker/?group_id=145963</url>
	</issueManagement>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.5</java.version>
		<aspectj.version>1.7.3</aspectj.version>
	</properties>

	<prerequisites>
		<maven>3.0.0</maven>
	</prerequisites>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.11</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
			<optional>true</optional>
			<exclusions>
				<exclusion>
					<groupId>javax.jms</groupId>
					<artifactId>jms</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jdmk</groupId>
					<artifactId>jmxtools</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.jmx</groupId>
					<artifactId>jmxri</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.mail</groupId>
					<artifactId>mail</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>${aspectj.version}</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>1.1.3</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.5</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<!-- cannot upgrade to 1.4.5 which results in xstream internal RuntimeException -->
			<version>1.4.4</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.beanshell</groupId>
			<artifactId>bsh</artifactId>
			<version>2.0b5</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>ognl</groupId>
			<artifactId>ognl</artifactId>
			<version>3.0.8</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.jruby</groupId>
			<artifactId>jruby</artifactId>
			<!-- cannot upgrade to 1.7, which requires Java6 or higher -->
			<!-- cannot upgrade to 1.6.6/7/8 because of a bug in jruby's Hash class -->
			<version>1.6.5</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.mozilla</groupId>
			<artifactId>rhino</artifactId>
			<version>1.7R4</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>commons-jxpath</groupId>
			<artifactId>commons-jxpath</artifactId>
			<version>1.3</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>javolution</groupId>
			<artifactId>javolution</artifactId>
			<version>5.5.1</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.google.inject</groupId>
			<artifactId>guice</artifactId>
			<version>3.0</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
			<version>3.2.5.RELEASE</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>3.2.5.RELEASE</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>aopalliance</groupId>
			<artifactId>aopalliance</artifactId>
			<version>1.0</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib-nodep</artifactId>
			<version>2.2.2</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-all</artifactId>
			<version>2.1.6</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-jexl</artifactId>
			<version>2.1.1</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.mvel</groupId>
			<artifactId>mvel2</artifactId>
			<!-- cannot upgrade to 2.1.x because of NPE in MVEL -->
			<version>2.0.19</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.thoughtworks.paranamer</groupId>
			<artifactId>paranamer</artifactId>
			<version>2.6</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>javax.persistence</groupId>
			<artifactId>persistence-api</artifactId>
			<version>1.0.2</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>net.sf.trove4j</groupId>
			<artifactId>trove4j</artifactId>
			<version>3.0.3</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
			<version>1.0.0.GA</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<!-- required for JSR223 Script Engine backport -->
			<groupId>javax.script</groupId>
			<artifactId>script-api</artifactId>
			<version>1.0</version>
			<classifier>jdk14</classifier>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<!-- required for JSR223 Script Engine backport -->
			<groupId>javax.script</groupId>
			<artifactId>js-engine</artifactId>
			<version>1.1</version>
			<classifier>jdk14</classifier>
			<scope>test</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<!-- required for JSR223 Script Engine backport -->
			<groupId>net.sf.retrotranslator</groupId>
			<artifactId>retrotranslator-runtime</artifactId>
			<version>1.2.9</version>
			<scope>test</scope>
			<optional>true</optional>
		</dependency>
	</dependencies>

	<profiles>
		<profile>
			<!-- http://blog.sonatype.com/people/2010/01/how-to-generate-pgp-signatures-with-maven -->
			<id>release-sign-artifacts</id>
			<activation>
				<property>
					<name>performRelease</name>
					<value>true</value>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<build>
		<plugins>
			<plugin>
				<!-- http://maven.apache.org/guides/mini/guide-using-toolchains.html -->
				<artifactId>maven-toolchains-plugin</artifactId>
				<version>1.0</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>toolchain</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<toolchains>
						<jdk>
							<version>${java.version}</version>
							<vendor>sun</vendor>
						</jdk>
					</toolchains>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>clirr-maven-plugin</artifactId>
				<version>2.6</version>
				<configuration>
					<minSeverity>info</minSeverity>
				</configuration>
			</plugin>


			<!--*********************************************************************
				phase: validate
				********************************************************************* -->
			<plugin>
				<artifactId>maven-enforcer-plugin</artifactId>
				<version>1.3.1</version>
				<executions>
					<execution>
						<id>enforce-no-snapshots</id>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<requireReleaseDeps>
									<message>No Snapshots Allowed!</message>
								</requireReleaseDeps>
							</rules>
							<fail>true</fail>
						</configuration>
					</execution>
				</executions>
			</plugin>


			<!--*********************************************************************
				phase: generate-sources
				********************************************************************* -->
			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>1.7</version>
				<executions>
					<execution>
						<phase>generate-sources</phase>
						<configuration>
							<target>
								<copy todir="${project.build.directory}">
									<fileset dir="${project.basedir}" includes="README.txt" />
								</copy>
								<replace file="${project.build.directory}/README.txt" token="$VERSION$" value="${project.version}" />
							</target>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>


			<!-- ********************************************************************* -->
			<!-- phase: generate-resources -->
			<!-- ********************************************************************* -->
			<plugin>
				<!-- better docbook support see http://docs.codehaus.org/display/MAVENUSER/Docbkx+Maven+Plugin
					manual: mvn docbkx:generate-html -->
				<groupId>com.agilejava.docbkx</groupId>
				<artifactId>docbkx-maven-plugin</artifactId>
				<version>2.0.14</version>
				<executions>
					<execution>
						<goals>
							<goal>generate-html</goal>
						</goals>
						<!-- needs to be in generate-resources and not pre-site since we also
							want to include the documentation in the distributions and not just the site -->
						<phase>generate-resources</phase>
					</execution>
				</executions>
				<configuration>
					<!-- see http://docbook.sourceforge.net/release/xsl/snapshot/doc/html/
						and http://www.agilejava.com/docbkx/docbkx-maven-plugin/generate-html-mojo.html -->
					<sourceDirectory>${project.basedir}/src/site/docbook</sourceDirectory>
					<targetDirectory>${project.build.directory}/site</targetDirectory>

					<xincludeSupported>true</xincludeSupported>
					<useExtensions>1</useExtensions>
					<calloutsExtension>1</calloutsExtension>

					<graphicDefaultExtension>png</graphicDefaultExtension>
					<tocSectionDepth>2</tocSectionDepth>

					<!-- configure syntax highlighting -->
					<highlightDefaultLanguage>java</highlightDefaultLanguage>
					<highlightSource>1</highlightSource>
					<htmlCustomization>${project.basedir}/src/site/docbook/highlight.xsl</htmlCustomization>
					<xhtmlCustomization>${project.basedir}/src/site/docbook/highlight.xsl</xhtmlCustomization>

					<!-- Enable auto numbering of section labels -->
					<sectionAutolabel>1</sectionAutolabel>

					<!-- Enable CSS support -->
					<cssDecoration>1</cssDecoration>
					<htmlStylesheet>css/userguide.css</htmlStylesheet>
					<tableBordersWithCss>1</tableBordersWithCss>

					<!-- Turn off rules below/above header/footer - we'll use CSS borders -->
					<headerRule>0</headerRule>
					<footerRule>0</footerRule>

					<!-- Turn off image scaling when generating HTML output -->
					<makeGraphicViewport>1</makeGraphicViewport>
					<ignoreImageScaling>1</ignoreImageScaling>

					<!-- Make external links stay within window (don't let them take over entire frame) -->
					<ulinkTarget>'_self'</ulinkTarget>

					<!-- Spend extra CPU time to try and produce valid and pretty HTML -->
					<htmlCleanup>1</htmlCleanup>
					<makeValidHtml>1</makeValidHtml>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>org.docbook</groupId>
						<artifactId>docbook-xml</artifactId>
						<version>4.4</version>
						<scope>runtime</scope>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>native2ascii-maven-plugin</artifactId>
				<version>1.0-beta-1</version>
				<executions>
					<execution>
						<goals>
							<goal>native2ascii</goal>
						</goals>
						<phase>generate-resources</phase>
					</execution>
				</executions>
				<configuration>
					<dest>${project.build.directory}/classes</dest>
					<src>${project.basedir}/src/main/resources</src>
					<includes>
						<include>**/*.properties</include>
					</includes>
					<excludes>
						<exclude>**/Messages_de.properties</exclude>
					</excludes>
				</configuration>
			</plugin>


			<!--*********************************************************************
				phase: compile
				********************************************************************* -->

			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<executions>
					<execution>
						<id>default-compile</id>
						<phase>none</phase>
					</execution>
					<execution>
						<id>default-testCompile</id>
						<phase>none</phase>
					</execution>
				</executions>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<compilerVersion>${java.version}</compilerVersion>
					<!-- disabled because of bug http://jira.codehaus.org/browse/MCOMPILER-208 -->
					<!--
						<compilerId>aspectj</compilerId>
						<includes>
						<include>**/*.java</include>
						<include>**/*.aj</include>
						</includes>
					-->
				</configuration>

				<!--
					<dependencies>
					<dependency>
					<groupId>org.codehaus.plexus</groupId>
					<artifactId>plexus-compiler-aspectj</artifactId>
					<version>2.2</version>
					<exclusions>
					<exclusion>
					<groupId>org.aspectj</groupId>
					<artifactId>aspectjrt</artifactId>
					</exclusion>
					<exclusion>
					<groupId>org.aspectj</groupId>
					<artifactId>aspectjtools</artifactId>
					</exclusion>
					</exclusions>
					</dependency>
					<dependency>
					<groupId>org.aspectj</groupId>
					<artifactId>aspectjtools</artifactId>
					<version>${aspectj.version}</version>
					</dependency>
					</dependencies> -->
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>aspectj-maven-plugin</artifactId>
				<version>1.2</version>
				<executions>
					<execution>
						<goals>
							<goal>compile</goal>
							<goal>test-compile</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<complianceLevel>${java.version}</complianceLevel>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<aspectDirectory>src/main/java</aspectDirectory>
					<testAspectDirectory>src/test/java</testAspectDirectory>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>org.aspectj</groupId>
						<artifactId>aspectjtools</artifactId>
						<version>${aspectj.version}</version>
					</dependency>
				</dependencies>
			</plugin>


			<!--*********************************************************************
				phase: test
				********************************************************************* -->
			<plugin>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.16</version>
				<configuration>
					<argLine>-Duser.language=en -Duser.region=US</argLine>
					<excludes>
						<exclude>**/TestGuardAspect*</exclude>
						<exclude>**/Abstract*</exclude>
					</excludes>
				</configuration>
			</plugin>


			<!--*********************************************************************
				phase: package
				********************************************************************* -->
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<!-- cannot use 2.4 because of
					http://dev.eclipse.org/mhonarc/lists/m2e-users/msg03633.html
					https://github.com/sonatype/m2eclipse-extras/issues/10
				-->
				<version>2.3.2</version>
				<configuration>
					<!-- <useDefaultManifestFile>true</useDefaultManifestFile> -->
					<archive>
						<manifestFile>${project.basedir}/src/main/java/META-INF/MANIFEST.MF</manifestFile>
						<addMavenDescriptor>false</addMavenDescriptor>
						<manifestEntries>
							<Bundle-Version>${project.version}</Bundle-Version>
							<Specification-Title>${project.name}</Specification-Title>
							<Specification-Version>${project.version}</Specification-Version>
							<Specification-Vendor>${project.organization.name}</Specification-Vendor>
							<Specification-URL>${project.url}</Specification-URL>
							<Implementation-Title>${project.name}</Implementation-Title>
							<Implementation-Version>${project.version}</Implementation-Version>
							<Implementation-Vendor-Id>${project.groupId}</Implementation-Vendor-Id>
							<Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
							<Implementation-URL>${project.url}</Implementation-URL>
						</manifestEntries>
					</archive>
					<excludes>
						<exclude>builddef.lst</exclude>
					</excludes>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>jar</goal>
						</goals>
						<phase>package</phase>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.9.1</version>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<!-- we are generating the site in the package phase because we want to include it in the distribution -->
				<artifactId>maven-site-plugin</artifactId>
				<version>3.3</version>
				<configuration>
					<!-- https://jira.codehaus.org/browse/WAGON-354 -->
					<chmod>false</chmod>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>site</goal>
						</goals>
						<phase>package</phase>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.2.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<!-- do not upload during deploy phase -->
					<attach>false</attach>
					<descriptors>
						<descriptor>${project.basedir}/src/main/assembly/archive-with-dependencies.xml</descriptor>
						<descriptor>${project.basedir}/src/main/assembly/archive-without-dependencies.xml</descriptor>
					</descriptors>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>single</goal>
						</goals>
						<phase>package</phase>
					</execution>
				</executions>
			</plugin>

			<!--*********************************************************************
				phase: release
				********************************************************************* -->
			<plugin>
				<artifactId>maven-release-plugin</artifactId>
				<version>2.4.2</version>
				<configuration>
					<autoVersionSubmodules>true</autoVersionSubmodules>
					<providerImplementations>
						<svn>javasvn</svn>
					</providerImplementations>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>com.google.code.maven-scm-provider-svnjava</groupId>
						<artifactId>maven-scm-provider-svnjava</artifactId>
						<version>2.0.6</version>
					</dependency>
				</dependencies>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-scm-plugin</artifactId>
				<version>1.8.1</version>
				<configuration>
					<providerImplementations>
						<svn>javasvn</svn>
					</providerImplementations>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>com.google.code.maven-scm-provider-svnjava</groupId>
						<artifactId>maven-scm-provider-svnjava</artifactId>
						<version>2.0.6</version>
					</dependency>
				</dependencies>
			</plugin>

			<plugin>
				<artifactId>maven-changes-plugin</artifactId>
				<version>2.9</version>
				<executions>
					<execution>
						<id>check-changes</id>
						<phase>verify</phase>
						<goals>
							<goal>changes-check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<!--*********************************************************************
				phase: manual execution
				********************************************************************* -->
			<plugin>
				<artifactId>maven-eclipse-plugin</artifactId>
				<version>2.9</version>
				<configuration>
					<downloadSources>true</downloadSources>
					<classpathContainers>
						<classpathContainer>org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/J2SE-${java.version}</classpathContainer>
					</classpathContainers>
					<additionalProjectnatures>
						<projectnature>org.eclipse.ajdt.ui.ajnature</projectnature>
					</additionalProjectnatures>
					<additionalBuildcommands>
						<buildcommand>org.eclipse.ajdt.core.ajbuilder</buildcommand>
					</additionalBuildcommands>
				</configuration>
			</plugin>
		</plugins>
		<extensions>
			<extension>
				<!-- sftp support -->
				<groupId>org.apache.maven.wagon</groupId>
				<artifactId>wagon-ssh</artifactId>
				<version>2.5</version>
			</extension>
		</extensions>
		<pluginManagement>
			<plugins>
				<!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself. -->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.apache.maven.plugins</groupId>
										<artifactId>maven-enforcer-plugin</artifactId>
										<versionRange>[1.2,)</versionRange>
										<goals>
											<goal>enforce</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.codehaus.mojo</groupId>
										<artifactId>native2ascii-maven-plugin</artifactId>
										<versionRange>[1.0-alpha-1,)</versionRange>
										<goals>
											<goal>native2ascii</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>com.agilejava.docbkx</groupId>
										<artifactId>docbkx-maven-plugin</artifactId>
										<versionRange>[2.0.14,)</versionRange>
										<goals>
											<goal>generate-html</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.apache.maven.plugins</groupId>
										<artifactId>maven-antrun-plugin</artifactId>
										<versionRange>[1.7,)</versionRange>
										<goals>
											<goal>run</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.codehaus.mojo</groupId>
										<artifactId>aspectj-maven-plugin</artifactId>
										<versionRange>[1.2,)</versionRange>
										<goals>
											<goal>compile</goal>
											<goal>test-compile</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore />
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<reporting>
		<plugins>

			<plugin>
				<!--
					The basic purpose of "Maven Dashboard Report Plugin" is to centralize and share all
					quality informations generated by other Maven report plugins
				-->
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>dashboard-maven-plugin</artifactId>
				<version>1.0.0-beta-1</version>
			</plugin>

			<plugin>
				<!-- Generate Javadoc for the project. -->
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.9.1</version>
				<configuration>
					<stylesheetfile>${project.basedir}/src/site/javadoc/oval-javadoc.css
					</stylesheetfile>
					<stylesheet>maven</stylesheet>
					<linksource>true</linksource>
					<links>
						<link>http://java.sun.com/j2se/${java.version}.0/docs/api/</link>
					</links>
					<source>${java.version}</source>
					<destDir>api</destDir>
				</configuration>
				<reportSets>
					<reportSet>
						<id>html</id>
						<reports>
							<report>javadoc</report>
						</reports>
					</reportSet>
				</reportSets>
			</plugin>

			<plugin>
				<!-- Creates an html-based, cross referenced version of Java source code for a project. -->
				<artifactId>maven-jxr-plugin</artifactId>
				<version>2.3</version>
				<configuration>
					<linkJavadoc>true</linkJavadoc>
					<javadocDir>api</javadocDir>
				</configuration>
			</plugin>

			<plugin>
				<!-- Generate a report based on the results of unit tests. -->
				<artifactId>maven-surefire-report-plugin</artifactId>
				<version>2.16</version>
				<reportSets>
					<reportSet>
						<reports>
							<report>report-only</report>
						</reports>
					</reportSet>
				</reportSets>
			</plugin>

			<plugin>
				<!-- TODO, @todo list report -->
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>taglist-maven-plugin</artifactId>
				<version>2.4</version>
			</plugin>

			<plugin>
				<!-- JUnit Code Coverage report -->
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>cobertura-maven-plugin</artifactId>
				<version>2.6</version>
				<configuration>
					<instrumentation>
						<excludes>
							<exclude>net/sf/oval/test/**/*.class</exclude>
						</excludes>
					</instrumentation>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>jdepend-maven-plugin</artifactId>
				<version>2.0-beta-2</version>
			</plugin>

			<plugin>
				<!-- This plugin produces a nicely formatted metrics report based on your project -->
				<artifactId>maven-pmd-plugin</artifactId>
				<version>3.0.1</version>
				<configuration>
					<linkXref>true</linkXref>
					<minimumTokens>100</minimumTokens>
					<targetJdk>${java.version}</targetJdk>
				</configuration>
			</plugin>

			<plugin>
				<!-- FindBugs looks for bugs in Java programs -->
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>findbugs-maven-plugin</artifactId>
				<version>2.5.2</version>
				<configuration>
					<excludeFilterFile>${project.basedir}/src/misc/findbugs-exclude.xml</excludeFilterFile>
					<xmlOutput>true</xmlOutput>
					<effort>Max</effort>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<version>2.10</version>
				<configuration>
					<configLocation>${project.basedir}/src/misc/checkstyle-config.xml</configLocation>
					<enableRSS>false</enableRSS>
					<failsOnError>false</failsOnError>
					<linkXref>true</linkXref>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>clirr-maven-plugin</artifactId>
				<version>2.6</version>
			</plugin>

			<plugin>
				<artifactId>maven-changes-plugin</artifactId>
				<version>2.9</version>
				<configuration>
					<xmlPath>${project.basedir}/src/site/changes.xml</xmlPath>
				</configuration>
				<reportSets>
					<reportSet>
						<reports>
							<report>changes-report</report>
						</reports>
					</reportSet>
				</reportSets>
			</plugin>
		</plugins>
	</reporting>

</project>