<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.github.houbb</groupId>
    <artifactId>opencc4j</artifactId>
    <version>1.6.0</version>

    <properties>
        <!--============================== All PLUGINS START ==============================-->
        <plugin.compiler.version>3.2</plugin.compiler.version>
        <plugin.compiler.version>3.2</plugin.compiler.version>
        <plugin.surefire.version>2.18.1</plugin.surefire.version>
        <plugin.surefire.skip-it>false</plugin.surefire.skip-it>
        <plugin.surefire.ignore-failure>false</plugin.surefire.ignore-failure>

        <plugin.gen.version>1.0.1</plugin.gen.version>
        <plugin.coveralls.version>4.3.0</plugin.coveralls.version>
        <plugin.cobertura.version>2.7</plugin.cobertura.version>
        <!--============================== All PLUGINS END ==============================-->

        <!--============================== MAIN START ==============================-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <compiler.level>1.7</compiler.level>
        <!--============================== MAIN START ==============================-->

        <!--============================== JAR START ==============================-->
        <!--inter-->
        <heaven.version>0.1.106</heaven.version>
        <nlp-common.version>0.0.2</nlp-common.version>

        <!--other-->
        <jieba-analysis.version>1.0.2</jieba-analysis.version>
        <junit.version>4.12</junit.version>
    </properties>

    <dependencies>
        <!--============================== INTER ==============================-->
        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>heaven</artifactId>
            <version>${heaven.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>nlp-common</artifactId>
            <version>${nlp-common.version}</version>
        </dependency>

        <!--============================== OTHER ==============================-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <optional>true</optional>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.huaban</groupId>
            <artifactId>jieba-analysis</artifactId>
            <version>${jieba-analysis.version}</version>
            <optional>true</optional>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.name}</finalName>

        <pluginManagement>
            <!--============================== All Plugins ==============================-->
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${plugin.compiler.version}</version>
                    <configuration>
                        <source>${compiler.level}</source>
                        <target>${compiler.level}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${plugin.surefire.version}</version>
                    <configuration>
                        <skipTests>${plugin.surefire.skip-it}</skipTests>
                        <testFailureIgnore>${plugin.surefire.ignore-failure}</testFailureIgnore>
                    </configuration>
                </plugin>

                <!--=================================== coveralls START ===================================-->
                <!--mvn cobertura:cobertura coveralls:report -DrepoToken=yourcoverallsprojectrepositorytoken-->
                <plugin>
                    <groupId>org.eluder.coveralls</groupId>
                    <artifactId>coveralls-maven-plugin</artifactId>
                    <version>${plugin.coveralls.version}</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>${plugin.cobertura.version}</version>
                    <configuration>
                        <format>xml</format>
                        <maxmem>256m</maxmem>
                        <!-- aggregated reports for multi-module projects -->
                        <aggregate>true</aggregate>
                        <instrumentation>
                            <excludes>
                                <exclude>**/*Test.class</exclude>
                                <!--<exclude>**/*Vo.class</exclude>-->
                            </excludes>
                        </instrumentation>
                    </configuration>
                </plugin>
                <!--=================================== coveralls END ===================================-->

            </plugins>
        </pluginManagement>

        <!--use plugins-->
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
            </plugin>

            <!--mvn clean install sonar:sonar -Dmaven.test.skip=true -Dsonar.host.url=http://localhost:9000-->
            <!--sonar-->
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.1.1</version>
            </plugin>

            <!--=================================== coveralls START ===================================-->
            <!--mvn cobertura:cobertura coveralls:report -DrepoToken=yourcoverallsprojectrepositorytoken-->
            <plugin>
                <groupId>org.eluder.coveralls</groupId>
                <artifactId>coveralls-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
            </plugin>
            <!--=================================== coveralls END ===================================-->
        </plugins>

    </build>

    <!--============================== ADD For sonatype START ==============================-->
    <name>opencc4j</name>
    <description>Opencc4j is an opensource project for conversion between Traditional Chinese and Simplified Chinese,
        supporting character-level conversion, phrase-level conversion for java.</description>

    <parent>
        <groupId>org.sonatype.oss</groupId>
        <artifactId>oss-parent</artifactId>
        <version>7</version>
    </parent>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <url>https://github.com/houbb/opencc4j</url>
        <connection>https://github.com/houbb/opencc4j.git</connection>
        <developerConnection>https://houbb.github.io/</developerConnection>
    </scm>
    <developers>
        <developer>
            <name>houbb</name>
            <email><EMAIL></email>
            <url>https://houbb.github.io/</url>
        </developer>
    </developers>
    <!--============================== ADD For sonatype END ==============================-->


    <!--mvn clean deploy -P release -Darguments="gpg.passphrase=PASSWORD"-->
    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <!-- Source -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>2.2.1</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Javadoc -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>2.9.1</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- GPG -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.5</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
            <distributionManagement>
                <snapshotRepository>
                    <id>oss</id>
                    <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
                </snapshotRepository>
                <repository>
                    <id>oss</id>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>

</project>