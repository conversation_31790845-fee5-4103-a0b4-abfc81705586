<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.kohsuke</groupId>
    <artifactId>pom</artifactId>
    <version>10</version>
  </parent>

  <artifactId>github-api</artifactId>
  <version>1.59</version>
  <name>GitHub API for Java</name>
  <url>http://github-api.kohsuke.org/</url>
  <description>GitHub API for Java</description>

  <scm>
    <connection>scm:git:**************/kohsuke/${project.artifactId}.git</connection>
    <developerConnection>scm:git:ssh://**************/kohsuke/${project.artifactId}.git</developerConnection>
    <url>http://${project.artifactId}.kohsuke.org/</url>
    <tag>HEAD</tag>
  </scm>

  <distributionManagement>
    <site>
      <id>github-pages</id>
      <url>gitsite:**************/kohsuke/${project.artifactId}.git</url>
    </site>
  </distributionManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>com.infradna.tool</groupId>
        <artifactId>bridge-method-injector</artifactId>
        <version>1.12</version>
        <executions>
          <execution>
            <goals>
              <goal>process</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>2.6</version>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.7</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.2.3</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>1.4</version>
    </dependency>
    <dependency>
      <groupId>com.infradna.tool</groupId>
      <artifactId>bridge-method-annotation</artifactId>
      <version>1.12</version>
    </dependency>
    <dependency>
      <groupId>org.kohsuke.stapler</groupId>
      <artifactId>stapler-jetty</artifactId>
      <version>1.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit</artifactId>
      <version>3.1.0.201310021548-r</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp</groupId>
      <artifactId>okhttp-urlconnection</artifactId>
      <version>2.0.0</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.kohsuke</groupId>
      <artifactId>wordnet-random-name</artifactId>
      <version>1.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-all</artifactId>
      <version>1.9.5</version>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>repo.jenkins-ci.org</id>
      <url>http://repo.jenkins-ci.org/public/</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>repo.jenkins-ci.org</id>
      <url>http://repo.jenkins-ci.org/public/</url>
    </pluginRepository>
  </pluginRepositories>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>

  <licenses>
    <license>
      <name>The MIT license</name>
      <url>http://www.opensource.org/licenses/mit-license.php</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
</project>
