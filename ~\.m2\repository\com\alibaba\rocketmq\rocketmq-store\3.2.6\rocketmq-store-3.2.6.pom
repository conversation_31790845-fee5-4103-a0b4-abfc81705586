<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.alibaba.rocketmq</groupId>
		<artifactId>rocketmq-all</artifactId>
		<version>3.2.6</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<packaging>jar</packaging>
	<artifactId>rocketmq-store</artifactId>
	<name>rocketmq-store ${project.version}</name>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>rocketmq-common</artifactId>
		</dependency>
	</dependencies>
</project>
